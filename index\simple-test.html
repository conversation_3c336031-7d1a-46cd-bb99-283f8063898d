<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple API Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Simple API Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="results"></div>

    <script>
        async function testAPI() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="info">Testing API...</div>';
            
            try {
                const response = await fetch('http://localhost:8000/api/offers', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                results.innerHTML += '<div class="info">Response status: ' + response.status + '</div>';
                
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                
                const data = await response.json();
                results.innerHTML += '<div class="success">API call successful!</div>';
                results.innerHTML += '<div class="info">Data received: ' + JSON.stringify(data, null, 2).substring(0, 500) + '...</div>';
                
                if (data.status === 'success' && data.data) {
                    results.innerHTML += '<div class="success">Found ' + data.data.length + ' offers</div>';
                    
                    // Display first 3 offers
                    const limitedOffers = data.data.slice(0, 3);
                    limitedOffers.forEach((offer, index) => {
                        results.innerHTML += '<div class="info">Offer ' + (index + 1) + ': ' + (offer.titre || 'No title') + '</div>';
                    });
                }
                
            } catch (error) {
                results.innerHTML += '<div class="error">Error: ' + error.message + '</div>';
                console.error('API test error:', error);
            }
        }
    </script>
</body>
</html>
