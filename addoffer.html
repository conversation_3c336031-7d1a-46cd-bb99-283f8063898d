<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Add Offer</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f3f4f6;
      margin: 0;
      padding: 20px;
    }
    .container {
      max-width: 800px;
      background: white;
      padding: 30px;
      margin: auto;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    h2 {
      margin-bottom: 30px;
      color: #111827;
      text-align: center;
    }
    .form-group {
      margin-bottom: 20px;
    }
    .form-row {
      display: flex;
      gap: 20px;
    }
    .form-row .form-group {
      flex: 1;
    }
    input, textarea, select {
      width: 100%;
      padding: 12px;
      margin-top: 8px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
      box-sizing: border-box;
    }
    input:focus, textarea:focus, select:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
    label {
      font-weight: bold;
      color: #374151;
      display: block;
    }
    .required {
      color: #dc2626;
    }
    button {
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 14px 28px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.2s;
    }
    button:hover:not(:disabled) {
      background-color: #1d4ed8;
      transform: translateY(-1px);
    }
    button:disabled {
      background-color: #9ca3af;
      cursor: not-allowed;
      transform: none;
    }
    .alert {
      padding: 12px 16px;
      border-radius: 6px;
      margin-bottom: 20px;
      display: none;
    }
    .alert-success {
      background-color: #d1fae5;
      border: 1px solid #10b981;
      color: #065f46;
    }
    .alert-error {
      background-color: #fee2e2;
      border: 1px solid #ef4444;
      color: #991b1b;
    }
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #ffffff;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s ease-in-out infinite;
      margin-right: 8px;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    .image-upload-section {
      border: 2px dashed #d1d5db;
      border-radius: 6px;
      padding: 20px;
      text-align: center;
      background-color: #f9fafb;
    }
    .image-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 15px;
    }
    .image-item {
      position: relative;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      padding: 10px;
      background: white;
      width: 150px;
    }
    .image-item img {
      width: 100%;
      height: 100px;
      object-fit: cover;
      border-radius: 4px;
    }
    .image-item .remove-btn {
      position: absolute;
      top: 5px;
      right: 5px;
      background: #ef4444;
      color: white;
      border: none;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      cursor: pointer;
      font-size: 12px;
    }
    .image-item .primary-checkbox {
      margin-top: 8px;
    }
    .image-item .alt-text {
      margin-top: 8px;
      padding: 4px;
      font-size: 12px;
    }
    .error-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    .error-list li {
      margin-bottom: 5px;
    }
    .hidden {
      display: none !important;
    }
    .select-images-btn {
      background: #6b7280;
      margin-bottom: 10px;
    }
    .upload-help-text {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
    }
    .submit-container {
      text-align: center;
      margin-top: 30px;
    }
  </style>
</head>
<body>

  <div class="container">
    <h2>Add a New Offer</h2>

    <!-- Alert Messages -->
    <div id="successAlert" class="alert alert-success">
      <strong>Success!</strong> <span id="successMessage"></span>
    </div>

    <div id="errorAlert" class="alert alert-error">
      <strong>Error!</strong> <span id="errorMessage"></span>
      <ul id="errorList" class="error-list"></ul>
    </div>

    <form id="offerForm" enctype="multipart/form-data">
      <!-- Basic Information -->
      <div class="form-row">
        <div class="form-group">
          <label for="titre">Title <span class="required">*</span></label>
          <input type="text" id="titre" name="titre" required>
        </div>
        <div class="form-group">
          <label for="prix">Price <span class="required">*</span></label>
          <input type="number" id="prix" name="prix" step="0.01" required>
        </div>
      </div>

      <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" name="description" rows="4"></textarea>
      </div>

      <!-- Dates -->
      <div class="form-row">
        <div class="form-group">
          <label for="date_debut">Start Date</label>
          <input type="date" id="date_debut" name="date_debut">
        </div>
        <div class="form-group">
          <label for="date_fin">End Date</label>
          <input type="date" id="date_fin" name="date_fin">
        </div>
        <div class="form-group">
          <label for="date_depart">Departure Date</label>
          <input type="date" id="date_depart" name="date_depart">
        </div>
      </div>

      <!-- Location Information -->
      <div class="form-row">
        <div class="form-group">
          <label for="point_depart">Departure Point</label>
          <input type="text" id="point_depart" name="point_depart">
        </div>
        <div class="form-group">
          <label for="destination">Destination</label>
          <input type="text" id="destination" name="destination">
        </div>
      </div>

      <!-- Transport and Accommodation -->
      <div class="form-row">
        <div class="form-group">
          <label for="type_transport">Transport Type</label>
          <input type="text" id="type_transport" name="type_transport" placeholder="e.g., Bus, Plane, Train">
        </div>
        <div class="form-group">
          <label for="type_hebergement">Accommodation Type</label>
          <input type="text" id="type_hebergement" name="type_hebergement" placeholder="e.g., Hotel, Resort, Hostel">
        </div>
        <div class="form-group">
          <label for="classement_hebergement">Accommodation Stars (1-5)</label>
          <input type="number" id="classement_hebergement" name="classement_hebergement" min="1" max="5">
        </div>
      </div>

      <!-- Inclusions -->
      <div class="form-row">
        <div class="form-group">
          <label for="repas_inclus">Meals Included</label>
          <select name="repas_inclus" id="repas_inclus">
            <option value="">Select</option>
            <option value="1">Yes</option>
            <option value="0">No</option>
          </select>
        </div>
        <div class="form-group">
          <label for="activites_incluses">Activities Included</label>
          <select name="activites_incluses" id="activites_incluses">
            <option value="">Select</option>
            <option value="1">Yes</option>
            <option value="0">No</option>
          </select>
        </div>
        <div class="form-group">
          <label for="adapte_aux_enfants">Child Friendly</label>
          <select name="adapte_aux_enfants" id="adapte_aux_enfants">
            <option value="">Select</option>
            <option value="1">Yes</option>
            <option value="0">No</option>
          </select>
        </div>
      </div>

      <!-- Group and Difficulty -->
      <div class="form-row">
        <div class="form-group">
          <label for="limite_taille_groupe">Group Size Limit</label>
          <input type="number" id="limite_taille_groupe" name="limite_taille_groupe" min="1">
        </div>
        <div class="form-group">
          <label for="places_disponibles">Available Spots</label>
          <input type="number" id="places_disponibles" name="places_disponibles" min="0">
        </div>
        <div class="form-group">
          <label for="niveau_difficulte">Difficulty Level</label>
          <input type="text" id="niveau_difficulte" name="niveau_difficulte" placeholder="e.g., Easy, Moderate, Hard">
        </div>
      </div>

      <div class="form-group">
        <label for="notes_supplementaires">Additional Notes</label>
        <textarea id="notes_supplementaires" name="notes_supplementaires" rows="3"></textarea>
      </div>

      <!-- Image Upload Section -->
      <div class="form-group">
        <label>Images</label>
        <div class="image-upload-section">
          <input type="file" id="images" name="images[]" multiple accept="image/*" class="hidden">
          <button type="button" id="selectImagesBtn" class="select-images-btn">
            Select Images
          </button>
          <p class="upload-help-text">
            Select multiple images for your offer. The first image will be set as primary by default.
          </p>
          <div id="imagePreview" class="image-preview"></div>
        </div>
      </div>

      <div class="submit-container">
        <button type="submit" id="submitBtn">
          <span id="submitText">Create Offer</span>
        </button>
      </div>
    </form>
  </div>

  <script>
    // Configuration
    const API_BASE_URL = 'http://localhost:8000/api';

    // DOM Elements
    const form = document.getElementById('offerForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const successAlert = document.getElementById('successAlert');
    const errorAlert = document.getElementById('errorAlert');
    const successMessage = document.getElementById('successMessage');
    const errorMessage = document.getElementById('errorMessage');
    const errorList = document.getElementById('errorList');
    const imagesInput = document.getElementById('images');
    const selectImagesBtn = document.getElementById('selectImagesBtn');
    const imagePreview = document.getElementById('imagePreview');

    // State
    let selectedImages = [];
    let isSubmitting = false;

    // Utility Functions
    function getAuthToken() {
      return localStorage.getItem('auth_token') || localStorage.getItem('access_token');
    }

    function showSuccess(message) {
      successMessage.textContent = message;
      successAlert.style.display = 'block';
      errorAlert.style.display = 'none';
      // Auto-hide after 5 seconds
      setTimeout(() => {
        successAlert.style.display = 'none';
      }, 5000);
    }

    function showError(message, errors = null) {
      errorMessage.textContent = message;
      errorList.innerHTML = '';

      if (errors) {
        Object.keys(errors).forEach(field => {
          errors[field].forEach(error => {
            const li = document.createElement('li');
            li.textContent = `${field}: ${error}`;
            errorList.appendChild(li);
          });
        });
      }

      errorAlert.style.display = 'block';
      successAlert.style.display = 'none';
    }

    function hideAlerts() {
      successAlert.style.display = 'none';
      errorAlert.style.display = 'none';
    }

    function setSubmitState(loading) {
      isSubmitting = loading;
      submitBtn.disabled = loading;

      if (loading) {
        submitText.innerHTML = '<span class="loading"></span>Creating Offer...';
      } else {
        submitText.textContent = 'Create Offer';
      }
    }

    // Image handling functions
    function handleImageSelection() {
      const files = Array.from(imagesInput.files);
      selectedImages = files.map((file, index) => ({
        file: file,
        preview: URL.createObjectURL(file),
        altText: '',
        isPrimary: index === 0, // First image is primary by default
        displayOrder: index + 1
      }));

      renderImagePreview();
    }

    function renderImagePreview() {
      imagePreview.innerHTML = '';

      selectedImages.forEach((image, index) => {
        const imageItem = document.createElement('div');
        imageItem.className = 'image-item';

        imageItem.innerHTML = `
          <button type="button" class="remove-btn" onclick="removeImage(${index})">&times;</button>
          <img src="${image.preview}" alt="Preview">
          <div class="primary-checkbox">
            <label>
              <input type="checkbox" ${image.isPrimary ? 'checked' : ''}
                     onchange="setPrimaryImage(${index}, this.checked)">
              Primary Image
            </label>
          </div>
          <input type="text" class="alt-text" placeholder="Alt text (optional)"
                 value="${image.altText}" onchange="setAltText(${index}, this.value)">
        `;

        imagePreview.appendChild(imageItem);
      });
    }

    function removeImage(index) {
      URL.revokeObjectURL(selectedImages[index].preview);
      selectedImages.splice(index, 1);

      // Reassign display orders and ensure we have a primary image
      selectedImages.forEach((img, idx) => {
        img.displayOrder = idx + 1;
      });

      if (selectedImages.length > 0 && !selectedImages.some(img => img.isPrimary)) {
        selectedImages[0].isPrimary = true;
      }

      renderImagePreview();
      updateFileInput();
    }

    function setPrimaryImage(index, isPrimary) {
      if (isPrimary) {
        // Unset all other primary flags
        selectedImages.forEach(img => img.isPrimary = false);
        selectedImages[index].isPrimary = true;
      } else {
        selectedImages[index].isPrimary = false;
        // Ensure at least one image is primary if we have images
        if (selectedImages.length > 0 && !selectedImages.some(img => img.isPrimary)) {
          selectedImages[0].isPrimary = true;
        }
      }
      renderImagePreview();
    }

    function setAltText(index, altText) {
      selectedImages[index].altText = altText;
    }

    function updateFileInput() {
      // Create a new FileList with the remaining files
      const dt = new DataTransfer();
      selectedImages.forEach(img => dt.items.add(img.file));
      imagesInput.files = dt.files;
    }

    // Event Listeners
    selectImagesBtn.addEventListener('click', () => {
      imagesInput.click();
    });

    imagesInput.addEventListener('change', handleImageSelection);

    // Form validation
    function validateForm() {
      const titre = document.getElementById('titre').value.trim();
      const prix = document.getElementById('prix').value;

      if (!titre) {
        showError('Title is required.');
        return false;
      }

      if (!prix || prix <= 0) {
        showError('Price is required and must be greater than 0.');
        return false;
      }

      return true;
    }

    // Form submission
    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      if (isSubmitting) return;

      hideAlerts();

      if (!validateForm()) {
        return;
      }

      const token = getAuthToken();
      if (!token) {
        showError('You must be logged in to create an offer. Please log in and try again.');
        return;
      }

      setSubmitState(true);

      try {
        const formData = new FormData(form);

        // Add image metadata if we have images
        if (selectedImages.length > 0) {
          // Remove the default images[] field
          formData.delete('images[]');

          // Add each image with its metadata
          selectedImages.forEach((image, index) => {
            formData.append('images[]', image.file);
            formData.append(`alt_text[${index}]`, image.altText);
            formData.append(`is_primary[${index}]`, image.isPrimary ? '1' : '0');
          });
        }

        const response = await fetch(`${API_BASE_URL}/offers`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
            // Don't set Content-Type for FormData
          },
          body: formData
        });

        const result = await response.json();

        if (response.ok) {
          showSuccess(result.message || 'Offer created successfully!');
          form.reset();
          selectedImages = [];
          renderImagePreview();

          // Redirect after 3 seconds
          setTimeout(() => {
            window.location.href = 'offers.html';
          }, 3000);
        } else {
          if (result.errors) {
            showError(result.message || 'Validation failed', result.errors);
          } else {
            showError(result.message || 'Failed to create offer');
          }
        }
      } catch (error) {
        console.error('Error creating offer:', error);
        showError('Network error: ' + error.message);
      } finally {
        setSubmitState(false);
      }
    });

    // Global functions for inline event handlers
    window.removeImage = removeImage;
    window.setPrimaryImage = setPrimaryImage;
    window.setAltText = setAltText;
  </script>

</body>
</html>
