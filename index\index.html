<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#ffffff">
    <title>Global Mojo Company</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="home.css">
    <script>
        // Tailwind configuration for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400;
        }
        .mobile-nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700;
        }
    </style>
    <!-- Load Navbar Script -->
    <script src="/components/load-navbar.js"></script>
</head>
<body class="font-sans antialiased text-gray-800 dark:text-white dark:bg-gray-900">
    <div class="page-loader">
        <div class="loader-spinner"></div>
    </div>

    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <!-- Hero Section -->
    <div class="hero-gradient text-white">
        <div class="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-extrabold tracking-tight sm:text-5xl lg:text-6xl">
                   Mojo Company
                </h1>
                <p class="mt-6 max-w-lg mx-auto text-xl">
                    Vols + Hôtels + Expériences uniques. Tout ce dont vous avez besoin pour des vacances parfaites.
                </p>
                <div class="mt-10 search-box bg-white rounded-lg p-6 max-w-4xl mx-auto">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <label for="destination" class="block text-sm font-medium text-gray-700 mb-1">Destination</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-map-marker-alt text-gray-400"></i>
                                </div>
                                <input type="text" id="destination" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Où voulez-vous aller ?">
                            </div>
                        </div>
                        <div class="flex-1">
                            <label for="dates" class="block text-sm font-medium text-gray-700 mb-1">Dates</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-calendar-alt text-gray-400"></i>
                                </div>
                                <input type="text" id="dates" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="JJ/MM/AAAA - JJ/MM/AAAA">
                            </div>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium transition duration-300 h-[42px]">
                                <i class="fas fa-search mr-2"></i>Rechercher
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Popular Destinations -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16" id="destinations">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Nos destinations populaires
            </h2>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
                Découvrez nos offres de voyage tout compris vers les plus belles destinations du monde
            </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">


        </div>

        <div class="mt-12 text-center">
            <a href="#" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 transition duration-300">
                Voir toutes les destinations
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
    </div>

    <!-- Parallax Section -->
    <div class="parallax bg-fixed relative py-32" style="background-image: url('https://images.unsplash.com/photo-1523482580672-f109ba8cb9be?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80')">
        <div class="absolute inset-0 bg-black/50"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
            <h2 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">
                Des expériences uniques à travers le monde
            </h2>
            <p class="mt-6 max-w-3xl mx-auto text-xl text-gray-100">
                Nous concevons des voyages sur mesure qui correspondent à vos rêves et à votre budget.
            </p>
            <div class="mt-10">
                <a href="#" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-blue-700 bg-white hover:bg-gray-50 transition duration-300">
                    <i class="fas fa-headset mr-2"></i> Parlez à un conseiller
                </a>
            </div>
        </div>
    </div>

    <!-- Hotel Search -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16" id="hotels">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Trouvez l'hôtel parfait
            </h2>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
                Recherchez parmi des milliers d'hôtels dans le monde entier et accédez directement à leurs sites web
            </p>
        </div>

        <!-- Formulaire de recherche adapté avec les IDs du JavaScript -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-12">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <label for="city" class="block text-sm font-medium text-gray-700 mb-1">Destination</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-map-marker-alt text-gray-400"></i>
                        </div>
                        <input type="text" id="city" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Où cherchez-vous un hôtel ?">
                    </div>
                </div>
                <div class="flex-1">
                    <label for="checkin" class="block text-sm font-medium text-gray-700 mb-1">Arrivée</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-400"></i>
                        </div>
                        <input type="date" id="checkin" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                <div class="flex-1">
                    <label for="checkout" class="block text-sm font-medium text-gray-700 mb-1">Départ</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-400"></i>
                        </div>
                        <input type="date" id="checkout" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                <div class="flex items-end">
                    <button type="button" id="searchBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium transition duration-300 h-[42px]">
                        <i class="fas fa-search mr-2"></i>Rechercher
                    </button>
                </div>
            </div>

            <!-- Message d'erreur -->
            <div id="error" class="mt-4 p-3 bg-red-100 text-red-700 rounded-md hidden">
                <p id="errorMessage"></p>
            </div>

            <!-- Debug info -->
            <div id="debug" class="mt-4 p-3 bg-gray-100 text-gray-700 rounded-md text-xs font-mono hidden">
                <p id="debugInfo"></p>
            </div>
        </div>

        <!-- Indicateur de chargement -->
        <div id="loading" class="text-center py-8 hidden">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Recherche en cours...</p>
        </div>

        <!-- Conteneur des résultats -->
        <div id="results" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Les résultats seront ajoutés ici par le JavaScript -->

            <!-- Exemples d'hôtels (seront remplacés par les résultats de recherche) -->
            <div class="hotel-card bg-white rounded-lg overflow-hidden shadow-md transition duration-300">
                <div class="relative h-48 overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="Hôtel Burj Al Arab" class="w-full h-full object-cover hotel-image transition duration-300">
                    <div class="absolute top-0 right-0 bg-yellow-500 text-white px-2 py-1 text-xs font-bold m-2 rounded">5★</div>
                </div>
                <div class="p-4">
                    <h3 class="text-xl font-bold mb-1">Burj Al Arab</h3>
                    <div class="flex items-center text-gray-600 text-sm mb-2">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        <span>Dubai, Émirats Arabes Unis</span>
                    </div>
                    <div class="flex items-center mb-3">
                        <div class="flex text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="text-gray-600 text-sm ml-2">(428 avis)</span>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">L'hôtel le plus luxueux de Dubai, offrant des suites somptueuses avec vue sur la mer et un service exceptionnel.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-blue-600">À partir de 899€/nuit</span>
                        <a href="https://www.burjalarab.com" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Site web <i class="fas fa-external-link-alt ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Autres exemples d'hôtels ici... -->
        </div>

        <!-- Modal pour les détails d'hôtel -->
        <div id="hotelDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center border-b pb-4">
                        <h2 id="modalHotelName" class="text-2xl font-bold text-gray-900"></h2>
                        <button type="button" id="closeModalBtn" class="text-gray-400 hover:text-gray-500">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Loading indicator in modal -->
                    <div id="modalLoading" class="py-12 text-center hidden">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                        <p class="mt-4 text-gray-600">Chargement des détails...</p>
                    </div>

                    <!-- Error message in modal -->
                    <div id="modalError" class="p-4 bg-red-100 text-red-700 rounded-md my-4 hidden">
                        <p id="modalErrorMessage"></p>
                    </div>

                    <!-- Modal content goes here -->
                    <div id="modalContent" class="pt-4"></div>
                </div>
            </div>
        </div>
    </div>



    <!-- Special Offers -->
    <div class="bg-gray-50 dark:bg-gray-800 py-16" id="offres">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white sm:text-4xl">
                    Nos offres spéciales
                </h2>
                <p class="mt-4 max-w-2xl text-xl text-gray-500 dark:text-gray-300 mx-auto">
                    Profitez de nos promotions exclusives sur les voyages tout compris
                </p>
            </div>

            <!-- Dynamic offers grid - will be populated by JavaScript -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="offers-grid">
                <!-- Loading indicator will be added here by JavaScript -->
            </div>

            <div class="mt-12 text-center flex justify-center">
                <a href="../offers/offers.html" id="view-all-offers" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 transition duration-300">
                    Voir toutes nos offres
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
                <!-- Admin-only button will be added here by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Testimonials -->


    <!-- Newsletter -->


    <!-- Footer -->
    <footer class="bg-gray-800 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">VoyageGlobe</h3>
                    <p class="mt-4 text-base text-gray-300">
                        Votre agence de voyage préférée pour des expériences uniques à travers le monde.
                    </p>
                    <div class="mt-4 flex space-x-6">
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Destinations</h3>
                    <ul class="mt-4 space-y-4">
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Europe</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Asie</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Amériques</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Afrique</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Océanie</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Entreprise</h3>
                    <ul class="mt-4 space-y-4">
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">À propos</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Carrières</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Blog</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Presse</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Partenariats</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Support</h3>
                    <ul class="mt-4 space-y-4">
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Centre d'aide</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">FAQ</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Conditions générales</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Confidentialité</a></li>
                        <li><a href="#" class="text-base text-gray-300 hover:text-white">Nous contacter</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-12 border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between">
                <p class="text-base text-gray-400">
                    &copy; 2023 VoyageGlobe. Tous droits réservés.
                </p>
                <div class="mt-4 md:mt-0 flex space-x-6">
                    <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/visa/visa-original.svg" class="h-8" alt="Visa">
                    <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mastercard/mastercard-original.svg" class="h-8" alt="Mastercard">
                    <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/paypal/paypal-original.svg" class="h-8" alt="Paypal">
                </div>
            </div>
        </div>
    </footer>
  <script src="./home.js"></script>
  <script src="../apihotel.js"></script>
  <script src="../offers.js"></script>
  <script src="offers-api.js"></script>
  <script src="darkmode.js"></script>
</body>
</html>