<!-- Navbar -->
<nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 transition-all duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <!-- Logo and primary nav -->
            <div class="flex">
                <div class="flex-shrink-0 flex items-center">
                    <a href="/index/index.html" class="flex items-center">
                        <i class="fas fa-globe-americas text-blue-600 dark:text-blue-400 text-2xl mr-2"></i>
                        Travel<span class="font-bold text-xl text-gray-900 dark:text-white">Galaxy</span>
                    </a>
                </div>
                <!-- Desktop Navigation -->
                <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                    <a href="/index/index.html" class="nav-link inline-flex items-center px-1 pt-1 border-b-2 border-transparent hover:border-blue-500 dark:hover:border-blue-400 h-full">
                        <i class="fas fa-home mr-1"></i> Accueil
                    </a>
                    <a href="/offers/offers.html" class="nav-link inline-flex items-center px-1 pt-1 border-b-2 border-transparent hover:border-blue-500 dark:hover:border-blue-400 h-full">
                        <i class="fas fa-tag mr-1"></i> Offres
                    </a>
                    <a href="/destinations/book.php" class="nav-link inline-flex items-center px-1 pt-1 border-b-2 border-transparent hover:border-blue-500 dark:hover:border-blue-400 h-full">
                        <i class="fas fa-plane mr-1"></i> Destinations
                    </a>
                    <a href="#" class="nav-link inline-flex items-center px-1 pt-1 border-b-2 border-transparent hover:border-blue-500 dark:hover:border-blue-400 h-full">
                        <i class="fas fa-info-circle mr-1"></i> À propos
                    </a>
                    <a href="#" class="nav-link inline-flex items-center px-1 pt-1 border-b-2 border-transparent hover:border-blue-500 dark:hover:border-blue-400 h-full">
                        <i class="fas fa-headset mr-1"></i> Contact
                    </a>
                </div>
            </div>

            <!-- Right side navigation -->
            <div class="hidden sm:ml-6 sm:flex sm:items-center space-x-4">
                <!-- Dark mode toggle -->
                <button id="theme-toggle" type="button" class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5">
                    <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                    <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
                    </svg>
                </button>

                <!-- User menu - Logged out state -->
                <div id="logged-out-menu" class="flex items-center space-x-2">
                    <a href="/auth/login.html" class="nav-link px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-sign-in-alt mr-1"></i> Connexion
                    </a>
                    <a href="/auth/register.html" class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-3 py-2 rounded-md text-sm font-medium transition duration-300">
                        <i class="fas fa-user-plus mr-1"></i> Inscription
                    </a>
                </div>

                <!-- User menu - Logged in state -->
                <div id="logged-in-menu" class="hidden relative user-menu">
                    <button type="button" class="user-menu-button bg-gray-100 dark:bg-gray-700 flex text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                        <span class="sr-only">Open user menu</span>
                        <div class="h-8 w-8 rounded-full bg-blue-500 dark:bg-blue-600 flex items-center justify-center text-white">
                            <span id="user-initials">U</span>
                        </div>
                    </button>

                    <!-- Dropdown menu -->
                    <div class="user-menu-dropdown origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none hidden z-50" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
                        <a href="/profile/profile.html" id="profile-link" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" role="menuitem">
                            <i class="fas fa-user mr-2"></i> Mon profil
                        </a>
                        <a href="/auth/admin/dashboard.html" id="admin-dashboard-link" class="hidden block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" role="menuitem">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard Admin
                        </a>
                        <a href="#" id="logout-button" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" role="menuitem">
                            <i class="fas fa-sign-out-alt mr-2"></i> Déconnexion
                        </a>
                    </div>
                </div>
            </div>

            <!-- Mobile menu button -->
            <div class="-mr-2 flex items-center sm:hidden">
                <button id="mobile-menu-button" type="button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500" aria-controls="mobile-menu" aria-expanded="false">
                    <span class="sr-only">Open main menu</span>
                    <i class="fas fa-bars"></i>
                </button>
                <!-- Dark mode toggle for mobile -->
                <button id="theme-toggle-mobile" type="button" class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 ml-2">
                    <svg id="theme-toggle-dark-icon-mobile" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                    <svg id="theme-toggle-light-icon-mobile" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state -->
    <div id="mobile-menu" class="sm:hidden hidden animate__animated">
        <div class="pt-2 pb-3 space-y-1">
            <a href="/index/index.html" class="mobile-nav-link block pl-3 pr-4 py-2 text-base font-medium border-l-4 border-transparent">
                <i class="fas fa-home mr-2"></i> Accueil
            </a>
            <a href="/offers/offers.html" class="mobile-nav-link block pl-3 pr-4 py-2 text-base font-medium border-l-4 border-transparent">
                <i class="fas fa-tag mr-2"></i> Offres
            </a>
            <a href="/destinations/book.php" class="mobile-nav-link block pl-3 pr-4 py-2 text-base font-medium border-l-4 border-transparent">
                <i class="fas fa-plane mr-2"></i> Destinations
            </a>
            <a href="#" class="mobile-nav-link block pl-3 pr-4 py-2 text-base font-medium border-l-4 border-transparent">
                <i class="fas fa-info-circle mr-2"></i> À propos
            </a>
            <a href="#" class="mobile-nav-link block pl-3 pr-4 py-2 text-base font-medium border-l-4 border-transparent">
                <i class="fas fa-headset mr-2"></i> Contact
            </a>
        </div>

        <!-- Mobile menu - Logged out state -->
        <div id="mobile-logged-out-menu" class="pt-4 pb-3 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center px-4 space-x-3">
                <a href="/auth/login.html" class="block text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
                    <i class="fas fa-sign-in-alt mr-2"></i> Connexion
                </a>
                <a href="/auth/register.html" class="block text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
                    <i class="fas fa-user-plus mr-2"></i> Inscription
                </a>
            </div>
        </div>

        <!-- Mobile menu - Logged in state -->
        <div id="mobile-logged-in-menu" class="hidden pt-4 pb-3 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center px-4">
                <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-blue-500 dark:bg-blue-600 flex items-center justify-center text-white">
                        <span id="mobile-user-initials">U</span>
                    </div>
                </div>
                <div class="ml-3">
                    <div id="mobile-user-name" class="text-base font-medium text-gray-800 dark:text-white">User Name</div>
                    <div id="mobile-user-email" class="text-sm font-medium text-gray-500 dark:text-gray-400"><EMAIL></div>
                </div>
            </div>
            <div class="mt-3 space-y-1">
                <a href="/profile/profile.html" id="mobile-profile-link" class="block px-4 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-user mr-2"></i> Mon profil
                </a>
                <a href="/auth/admin/dashboard.html" id="mobile-admin-dashboard-link" class="hidden block px-4 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard Admin
                </a>
                <a href="#" id="mobile-logout-button" class="block px-4 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-sign-out-alt mr-2"></i> Déconnexion
                </a>
            </div>
        </div>
    </div>
</nav>
