// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in and is an admin
    checkAdminAuth();

    // Initialize page
    initPage();

    // Add event listeners
    addEventListeners();
});

// Global variables
let allUsers = [];
let filteredUsers = [];
let currentPage = 1;
let itemsPerPage = 10;
let selectedUserId = null;

// Function to check if user is logged in and is an admin
function checkAdminAuth() {
    const token = localStorage.getItem('auth_token');
    const userRole = localStorage.getItem('user_role');

    if (!token) {
        // User is not logged in, redirect to login page
        window.location.href = '../login.html';
        return;
    }

    if (userRole !== 'admin') {
        // User is not an admin, redirect to home page
        alert('Access denied: You do not have admin privileges.');
        window.location.href = '/index/index.html';
        return;
    }

    // User is logged in and is an admin, fetch user data
    fetchUserData();
}

// Function to fetch user data
function fetchUserData() {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (userData) {
        try {
            const user = JSON.parse(userData);
            const name = user.name || (user.data && user.data.name) || 'Admin';

            // Update admin name in sidebar
            const adminNameElement = document.getElementById('admin-name');
            if (adminNameElement) {
                adminNameElement.textContent = name;
            }
        } catch (error) {
            console.error('Error parsing user data:', error);
        }
    }

    // Refresh user data from API
    fetch('http://localhost:8000/api/auth/me', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch user data: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('User data fetched:', data);

        // Store updated user data
        localStorage.setItem('user_data', JSON.stringify(data));

        // Update admin name in sidebar
        const name = data.name || (data.data && data.data.name) || 'Admin';
        const adminNameElement = document.getElementById('admin-name');
        if (adminNameElement) {
            adminNameElement.textContent = name;
        }
    })
    .catch(error => {
        console.error('Error fetching user data:', error);
    });
}

// Function to initialize page
function initPage() {
    // Fetch users from API
    fetchUsers();
}

// Function to fetch users from API
function fetchUsers() {
    const token = localStorage.getItem('auth_token');

    // Show loading state
    const usersTable = document.getElementById('users-table');
    if (usersTable) {
        usersTable.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    <div class="flex justify-center items-center">
                        <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500 mr-3"></div>
                        Loading users...
                    </div>
                </td>
            </tr>
        `;
    }

    // Fetch users from API
    fetch('http://localhost:8000/api/users', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch users: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Users fetched:', data);

        // Extract users data
        if (Array.isArray(data)) {
            allUsers = data;
        } else if (data.data && Array.isArray(data.data)) {
            allUsers = data.data;
        } else if (data.users && Array.isArray(data.users)) {
            allUsers = data.users;
        } else {
            allUsers = [];
            console.warn('Unexpected API response format:', data);
        }

        // Apply filters to update the table
        applyFilters();
    })
    .catch(error => {
        console.error('Error fetching users:', error);

        // Show error message in table
        if (usersTable) {
            usersTable.innerHTML = `
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-red-500 dark:text-red-400">
                        Error loading users: ${error.message}
                    </td>
                </tr>
            `;
        }

        // Set empty users array
        allUsers = [];
    });
}

// Function to apply filters
function applyFilters() {
    const searchTerm = document.getElementById('search').value.toLowerCase();
    const roleFilter = document.getElementById('role-filter').value;

    // Filter users based on search term and role
    filteredUsers = allUsers.filter(user => {
        // Search term filter
        const matchesSearch =
            (user.name && user.name.toLowerCase().includes(searchTerm)) ||
            (user.email && user.email.toLowerCase().includes(searchTerm)) ||
            (user.phone && user.phone.toLowerCase().includes(searchTerm));

        // Role filter
        let matchesRole = true;
        if (roleFilter !== 'all') {
            matchesRole = user.role === roleFilter;
        }

        return matchesSearch && matchesRole;
    });

    // Reset to first page
    currentPage = 1;

    // Update table and pagination
    updateTable();
    updatePagination();
}

// Function to update table
function updateTable() {
    const usersTable = document.getElementById('users-table');

    if (!usersTable) return;

    if (filteredUsers.length === 0) {
        usersTable.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    No users found.
                </td>
            </tr>
        `;
        return;
    }

    // Calculate pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredUsers.length);
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    // Update pagination info
    document.getElementById('page-start').textContent = startIndex + 1;
    document.getElementById('page-end').textContent = endIndex;
    document.getElementById('total-items').textContent = filteredUsers.length;

    // Clear table
    usersTable.innerHTML = '';

    // Add users to table
    paginatedUsers.forEach(user => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150';

        // Format date
        const joinedDate = new Date(user.created_at);
        const formattedDate = joinedDate.toLocaleDateString();

        // Create role badge
        const roleBadge = user.role === 'admin'
            ? '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300">Admin</span>'
            : '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300">Client</span>';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                        <span class="text-gray-600 dark:text-gray-300 font-medium">${user.name.charAt(0)}</span>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">${user.name}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${user.email}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${user.phone || 'N/A'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                ${roleBadge}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${formattedDate}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <a href="#" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 view-user" data-user-id="${user.id}">View</a>
            </td>
        `;

        usersTable.appendChild(row);
    });

    // Add event listeners to view buttons
    document.querySelectorAll('.view-user').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            selectedUserId = this.getAttribute('data-user-id');
            showUserModal(selectedUserId);
        });
    });
}

// Function to update pagination
function updatePagination() {
    const paginationContainer = document.getElementById('pagination');
    const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);

    // Update mobile pagination buttons
    const mobilePrevPage = document.getElementById('mobile-prev-page');
    const mobileNextPage = document.getElementById('mobile-next-page');

    mobilePrevPage.disabled = currentPage === 1;
    mobileNextPage.disabled = currentPage === totalPages;

    // Update desktop pagination buttons
    const prevPage = document.getElementById('prev-page');
    const nextPage = document.getElementById('next-page');

    prevPage.disabled = currentPage === 1;
    nextPage.disabled = currentPage === totalPages;

    // Remove existing page buttons
    const pageButtons = paginationContainer.querySelectorAll('.page-button');
    pageButtons.forEach(button => button.remove());

    // Add page buttons
    for (let i = 1; i <= totalPages; i++) {
        const pageButton = document.createElement('button');
        pageButton.className = `page-button relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium ${
            i === currentPage
                ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30'
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
        }`;
        pageButton.textContent = i;
        pageButton.addEventListener('click', () => {
            currentPage = i;
            updateTable();
            updatePagination();
        });

        // Insert before the next button
        paginationContainer.insertBefore(pageButton, nextPage);
    }
}

// Function to show user modal
function showUserModal(userId) {
    const userModal = document.getElementById('user-modal');
    const userDetails = document.getElementById('user-details');

    // Show modal
    userModal.classList.remove('hidden');

    // Find user by ID
    const user = allUsers.find(u => u.id == userId);

    if (!user) {
        userDetails.innerHTML = `
            <div class="text-center text-red-500 dark:text-red-400">
                User not found.
            </div>
        `;
        return;
    }

    // Format date
    const joinedDate = new Date(user.created_at);
    const formattedDate = joinedDate.toLocaleDateString();

    // Create role badge
    const roleBadge = user.role === 'admin'
        ? '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300">Admin</span>'
        : '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300">Client</span>';

    // Update user details
    userDetails.innerHTML = `
        <div class="flex justify-center mb-4">
            <div class="h-20 w-20 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 text-2xl font-bold">
                ${user.name.charAt(0)}
            </div>
        </div>
        <div class="text-center mb-4">
            <h4 class="text-lg font-bold text-gray-900 dark:text-white">${user.name}</h4>
            <div class="mt-1">${roleBadge}</div>
        </div>
        <div class="space-y-3 text-sm">
            <div class="flex justify-between">
                <span class="text-gray-500 dark:text-gray-400">Email:</span>
                <span class="text-gray-900 dark:text-white font-medium">${user.email}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-500 dark:text-gray-400">Phone:</span>
                <span class="text-gray-900 dark:text-white font-medium">${user.phone || 'N/A'}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-500 dark:text-gray-400">Joined:</span>
                <span class="text-gray-900 dark:text-white font-medium">${formattedDate}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-500 dark:text-gray-400">User ID:</span>
                <span class="text-gray-900 dark:text-white font-medium">${user.id}</span>
            </div>
        </div>
    `;
}

// Function to hide user modal
function hideUserModal() {
    const userModal = document.getElementById('user-modal');
    userModal.classList.add('hidden');
}

// Function to add event listeners
function addEventListeners() {
    // Search button
    const searchBtn = document.getElementById('search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', applyFilters);
    }

    // Search input (search on Enter key)
    const searchInput = document.getElementById('search');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    }

    // Role filter
    const roleFilter = document.getElementById('role-filter');
    if (roleFilter) {
        roleFilter.addEventListener('change', applyFilters);
    }

    // Pagination buttons
    const prevPage = document.getElementById('prev-page');
    const nextPage = document.getElementById('next-page');
    const mobilePrevPage = document.getElementById('mobile-prev-page');
    const mobileNextPage = document.getElementById('mobile-next-page');

    if (prevPage) {
        prevPage.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                updateTable();
                updatePagination();
            }
        });
    }

    if (nextPage) {
        nextPage.addEventListener('click', function() {
            const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                updateTable();
                updatePagination();
            }
        });
    }

    if (mobilePrevPage) {
        mobilePrevPage.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                updateTable();
                updatePagination();
            }
        });
    }

    if (mobileNextPage) {
        mobileNextPage.addEventListener('click', function() {
            const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                updateTable();
                updatePagination();
            }
        });
    }

    // User modal close buttons
    const closeUserModal = document.getElementById('close-user-modal');
    const closeUserBtn = document.getElementById('close-user-btn');

    if (closeUserModal) {
        closeUserModal.addEventListener('click', hideUserModal);
    }

    if (closeUserBtn) {
        closeUserBtn.addEventListener('click', hideUserModal);
    }

    // No need for add offer button event listener as we're using a direct link

    // Logout button
    const logoutBtn = document.getElementById('logout-sidebar-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// Function to handle logout
function logout() {
    const token = localStorage.getItem('auth_token');

    if (token) {
        // Call logout API
        fetch('http://localhost:8000/api/auth/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            }
        })
        .then(response => {
            // Clear local storage regardless of API response
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_role');

            // Redirect to login page
            window.location.href = '../login.html';
        })
        .catch(error => {
            console.error('Logout error:', error);
            // Still clear local storage and redirect on error
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_role');
            window.location.href = '../login.html';
        });
    } else {
        // If no token, just redirect
        window.location.href = '../login.html';
    }
}
