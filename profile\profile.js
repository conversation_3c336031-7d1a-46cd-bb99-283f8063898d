// Profile Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dark mode first
    initializeDarkMode();

    // Check authentication
    checkAuthentication();

    // Initialize profile page
    initializeProfile();

    // Setup event listeners
    setupEventListeners();

    // Load user data
    loadUserData();

    // Load reservations
    loadReservations();
});

// Initialize dark mode
function initializeDarkMode() {
    // Set initial theme based on localStorage or system preference
    if (localStorage.getItem('color-theme') === 'dark' ||
        (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }

    // Listen for theme changes from other pages/tabs
    window.addEventListener('storage', function(e) {
        if (e.key === 'color-theme') {
            if (e.newValue === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }
    });

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        if (!localStorage.getItem('color-theme')) {
            if (e.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }
    });
}

// Check if user is authenticated
function checkAuthentication() {
    const token = localStorage.getItem('auth_token');
    if (!token) {
        // Redirect to login if not authenticated
        window.location.href = '../auth/login.html';
        return;
    }

    // Check token expiration
    const expiresAt = localStorage.getItem('token_expires_at');
    if (expiresAt && new Date(expiresAt) < new Date()) {
        // Token expired, clear storage and redirect
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        localStorage.removeItem('user_role');
        localStorage.removeItem('token_expires_at');
        window.location.href = '../auth/login.html';
        return;
    }
}

// Initialize profile page
function initializeProfile() {
    // Set active tab
    const profileTab = document.getElementById('tab-profile');
    const reservationsTab = document.getElementById('tab-reservations');

    if (profileTab) {
        profileTab.classList.add('active');
    }

    // Show profile content by default
    showTab('profile');
}

// Setup event listeners
function setupEventListeners() {
    // Tab switching
    const profileTab = document.getElementById('tab-profile');
    const reservationsTab = document.getElementById('tab-reservations');

    if (profileTab) {
        profileTab.addEventListener('click', () => switchTab('profile'));
    }

    if (reservationsTab) {
        reservationsTab.addEventListener('click', () => switchTab('reservations'));
    }

    // Edit profile modal
    const editProfileBtn = document.getElementById('edit-profile-btn');
    const closeEditModal = document.getElementById('close-edit-modal');
    const cancelEditBtn = document.getElementById('cancel-edit-btn');
    const editProfileForm = document.getElementById('edit-profile-form');

    if (editProfileBtn) {
        editProfileBtn.addEventListener('click', openEditModal);
    }

    if (closeEditModal) {
        closeEditModal.addEventListener('click', closeEditProfileModal);
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', closeEditProfileModal);
    }

    if (editProfileForm) {
        editProfileForm.addEventListener('submit', handleProfileUpdate);
    }

    // Reservation details modal
    const closeReservationModal = document.getElementById('close-reservation-modal');
    if (closeReservationModal) {
        closeReservationModal.addEventListener('click', closeReservationDetailsModal);
    }

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        const editModal = document.getElementById('edit-profile-modal');
        const reservationModal = document.getElementById('reservation-details-modal');

        if (event.target === editModal) {
            closeEditProfileModal();
        }

        if (event.target === reservationModal) {
            closeReservationDetailsModal();
        }
    });
}

// Load user data from localStorage and API
function loadUserData() {
    const userData = localStorage.getItem('user_data');
    const userRole = localStorage.getItem('user_role');

    if (userData) {
        try {
            const user = JSON.parse(userData);
            displayUserData(user, userRole);
        } catch (error) {
            console.error('Error parsing user data:', error);
            fetchUserDataFromAPI();
        }
    } else {
        fetchUserDataFromAPI();
    }
}

// Fetch user data from API
function fetchUserDataFromAPI() {
    const token = localStorage.getItem('auth_token');

    fetch('http://localhost:8000/api/auth/me', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch user data: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('User data fetched:', data);

        // Store updated user data
        localStorage.setItem('user_data', JSON.stringify(data));

        // Extract role
        let userRole = 'client';
        if (data.role) {
            userRole = data.role.toLowerCase();
        } else if (data.data && data.data.role) {
            userRole = data.data.role.toLowerCase();
        }

        localStorage.setItem('user_role', userRole);

        // Display user data
        displayUserData(data, userRole);
    })
    .catch(error => {
        console.error('Error fetching user data:', error);
        showError('Failed to load user data');
    });
}

// Display user data in the UI
function displayUserData(userData, userRole) {
    // Extract user information
    const user = userData.data || userData;
    const name = user.name || user.nom || 'User';
    const email = user.email || '';
    const phone = user.phone || user.telephone || '';
    const role = userRole || user.role || 'client';
    const createdAt = user.created_at || user.date_creation || '';

    // Update header
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');
    const userRoleElement = document.getElementById('user-role');
    const userInitialsElement = document.getElementById('user-initials');

    if (userNameElement) userNameElement.textContent = name;
    if (userEmailElement) userEmailElement.textContent = email;
    if (userRoleElement) userRoleElement.textContent = role.charAt(0).toUpperCase() + role.slice(1);

    // Set user initials
    if (userInitialsElement) {
        const initials = name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
        userInitialsElement.textContent = initials || 'U';
    }

    // Update profile information
    const profileNameElement = document.getElementById('profile-name');
    const profileEmailElement = document.getElementById('profile-email');
    const profilePhoneElement = document.getElementById('profile-phone');
    const profileCreatedElement = document.getElementById('profile-created');

    if (profileNameElement) profileNameElement.textContent = name;
    if (profileEmailElement) profileEmailElement.textContent = email;
    if (profilePhoneElement) profilePhoneElement.textContent = phone || 'Non spécifié';

    if (profileCreatedElement && createdAt) {
        const date = new Date(createdAt);
        profileCreatedElement.textContent = date.toLocaleDateString('fr-FR');
    }
}

// Switch between tabs
function switchTab(tabName) {
    // Update tab buttons
    const profileTab = document.getElementById('tab-profile');
    const reservationsTab = document.getElementById('tab-reservations');

    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(tab => {
        tab.classList.remove('border-blue-500', 'dark:border-blue-400', 'text-blue-600', 'dark:text-blue-400');
        tab.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    });

    // Add active class to selected tab
    if (tabName === 'profile' && profileTab) {
        profileTab.classList.add('border-blue-500', 'dark:border-blue-400', 'text-blue-600', 'dark:text-blue-400');
        profileTab.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    } else if (tabName === 'reservations' && reservationsTab) {
        reservationsTab.classList.add('border-blue-500', 'dark:border-blue-400', 'text-blue-600', 'dark:text-blue-400');
        reservationsTab.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    }

    // Show corresponding content
    showTab(tabName);
}

// Show tab content
function showTab(tabName) {
    const profileContent = document.getElementById('profile-content');
    const reservationsContent = document.getElementById('reservations-content');

    if (tabName === 'profile') {
        if (profileContent) profileContent.classList.remove('hidden');
        if (reservationsContent) reservationsContent.classList.add('hidden');
    } else if (tabName === 'reservations') {
        if (profileContent) profileContent.classList.add('hidden');
        if (reservationsContent) reservationsContent.classList.remove('hidden');

        // Load reservations if not already loaded
        loadReservations();
    }
}

// Open edit profile modal
function openEditModal() {
    const modal = document.getElementById('edit-profile-modal');
    const userData = localStorage.getItem('user_data');

    if (userData) {
        try {
            const user = JSON.parse(userData);
            const userInfo = user.data || user;

            // Populate form fields
            const nameField = document.getElementById('edit-name');
            const emailField = document.getElementById('edit-email');
            const phoneField = document.getElementById('edit-phone');

            if (nameField) nameField.value = userInfo.name || userInfo.nom || '';
            if (emailField) emailField.value = userInfo.email || '';
            if (phoneField) phoneField.value = userInfo.phone || userInfo.telephone || '';

        } catch (error) {
            console.error('Error parsing user data:', error);
        }
    }

    if (modal) {
        modal.classList.remove('hidden');
    }
}

// Close edit profile modal
function closeEditProfileModal() {
    const modal = document.getElementById('edit-profile-modal');
    const messagesDiv = document.getElementById('edit-profile-messages');

    if (modal) {
        modal.classList.add('hidden');
    }

    // Clear any messages
    if (messagesDiv) {
        messagesDiv.innerHTML = '';
    }

    // Reset form
    const form = document.getElementById('edit-profile-form');
    if (form) {
        form.reset();
    }
}

// Handle profile update
function handleProfileUpdate(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const saveButton = document.getElementById('save-profile-btn');
    const messagesDiv = document.getElementById('edit-profile-messages');

    // Get form values
    const name = formData.get('name');
    const email = formData.get('email');
    const phone = formData.get('phone');

    // Validate form
    if (!name || !email) {
        showFormError('Le nom et l\'email sont requis.');
        return;
    }

    // Disable save button
    if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Enregistrement...';
    }

    // Clear previous messages
    if (messagesDiv) {
        messagesDiv.innerHTML = '';
    }

    // Update profile via API
    updateProfile(name, email, phone)
        .then(response => {
            showFormSuccess('Profil mis à jour avec succès !');

            // Update localStorage
            updateLocalStorageUserData(name, email, phone);

            // Refresh displayed data
            loadUserData();

            // Close modal after delay
            setTimeout(() => {
                closeEditProfileModal();
            }, 1500);
        })
        .catch(error => {
            console.error('Profile update error:', error);
            showFormError(error.message || 'Erreur lors de la mise à jour du profil.');
        })
        .finally(() => {
            // Re-enable save button
            if (saveButton) {
                saveButton.disabled = false;
                saveButton.innerHTML = 'Enregistrer';
            }
        });
}

// Update profile via API
function updateProfile(name, email, phone) {
    const token = localStorage.getItem('auth_token');

    return fetch('http://localhost:8000/api/auth/profile', {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: name,
            email: email,
            phone: phone
        })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            });
        }
        return response.json();
    });
}

// Update user data in localStorage
function updateLocalStorageUserData(name, email, phone) {
    const userData = localStorage.getItem('user_data');

    if (userData) {
        try {
            const user = JSON.parse(userData);

            // Update user data
            if (user.data) {
                user.data.name = name;
                user.data.email = email;
                user.data.phone = phone;
            } else {
                user.name = name;
                user.email = email;
                user.phone = phone;
            }

            // Save back to localStorage
            localStorage.setItem('user_data', JSON.stringify(user));

        } catch (error) {
            console.error('Error updating localStorage user data:', error);
        }
    }
}

// Load user reservations
function loadReservations() {
    const token = localStorage.getItem('auth_token');
    const container = document.getElementById('reservations-container');

    if (!container) return;

    // Show loading state
    container.innerHTML = `
        <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mr-3"></div>
            <span>Chargement des réservations...</span>
        </div>
    `;

    fetch('http://localhost:8000/api/reservations/user', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch reservations: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Reservations fetched:', data);
        displayReservations(data);
    })
    .catch(error => {
        console.error('Error fetching reservations:', error);
        container.innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-3xl mb-4"></i>
                <p class="text-gray-600 dark:text-gray-400">Erreur lors du chargement des réservations</p>
            </div>
        `;
    });
}

// Display reservations
function displayReservations(reservationsData) {
    const container = document.getElementById('reservations-container');
    if (!container) return;

    const reservations = reservationsData.data || reservationsData;

    if (!reservations || reservations.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-calendar-times text-gray-400 text-3xl mb-4"></i>
                <p class="text-gray-600 dark:text-gray-400">Aucune réservation trouvée</p>
            </div>
        `;
        return;
    }

    const reservationsHTML = reservations.map(reservation => {
        const status = getReservationStatus(reservation.status);
        const checkIn = new Date(reservation.check_in).toLocaleDateString('fr-FR');
        const checkOut = new Date(reservation.check_out).toLocaleDateString('fr-FR');

        return `
            <div class="card-modern rounded-xl p-6 hover:scale-[1.02] transition-all duration-300">
                <div class="flex justify-between items-start mb-6">
                    <div class="space-y-2">
                        <h3 class="text-xl font-bold text-primary-modern">${reservation.hotel_name || 'Hôtel'}</h3>
                        <p class="text-secondary-modern font-medium">${reservation.room_type || 'Chambre'}</p>
                    </div>
                    <span class="badge-modern px-4 py-2 rounded-full text-sm font-semibold ${status.class}">
                        ${status.text}
                    </span>
                </div>
                <div class="grid grid-cols-2 gap-6 mb-6">
                    <div class="space-y-1">
                        <p class="text-muted-modern text-xs font-medium uppercase tracking-wider">Arrivée</p>
                        <p class="text-primary-modern font-semibold text-lg">${checkIn}</p>
                    </div>
                    <div class="space-y-1">
                        <p class="text-muted-modern text-xs font-medium uppercase tracking-wider">Départ</p>
                        <p class="text-primary-modern font-semibold text-lg">${checkOut}</p>
                    </div>
                </div>
                <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="space-y-1">
                        <p class="text-muted-modern text-xs font-medium uppercase tracking-wider">Total</p>
                        <p class="text-2xl font-bold text-blue-600 dark:text-blue-400 glow-accent">${reservation.total_price || 0}€</p>
                    </div>
                    <button onclick="showReservationDetails(${reservation.id})"
                            class="btn-modern px-4 py-2 rounded-lg font-medium hover:glow-accent">
                        <i class="fas fa-eye mr-2"></i>Voir détails
                    </button>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = reservationsHTML;
}

// Get reservation status styling
function getReservationStatus(status) {
    switch (status?.toLowerCase()) {
        case 'confirmed':
        case 'confirmé':
            return {
                text: 'Confirmé',
                class: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
            };
        case 'pending':
        case 'en_attente':
            return {
                text: 'En attente',
                class: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
            };
        case 'cancelled':
        case 'annulé':
            return {
                text: 'Annulé',
                class: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
            };
        case 'completed':
        case 'terminé':
            return {
                text: 'Terminé',
                class: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
            };
        default:
            return {
                text: status || 'Inconnu',
                class: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
            };
    }
}

// Show reservation details
function showReservationDetails(reservationId) {
    const token = localStorage.getItem('auth_token');
    const modal = document.getElementById('reservation-details-modal');
    const detailsContainer = document.getElementById('reservation-details');

    if (!modal || !detailsContainer) return;

    // Show modal
    modal.classList.remove('hidden');

    // Show loading state
    detailsContainer.innerHTML = `
        <div class="flex justify-center">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
    `;

    // Fetch reservation details
    fetch(`http://localhost:8000/api/reservations/${reservationId}`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch reservation details: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Reservation details fetched:', data);
        displayReservationDetails(data);
    })
    .catch(error => {
        console.error('Error fetching reservation details:', error);
        detailsContainer.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl mb-2"></i>
                <p class="text-gray-600 dark:text-gray-400">Erreur lors du chargement des détails</p>
            </div>
        `;
    });
}

// Display reservation details in modal
function displayReservationDetails(reservationData) {
    const detailsContainer = document.getElementById('reservation-details');
    if (!detailsContainer) return;

    const reservation = reservationData.data || reservationData;
    const status = getReservationStatus(reservation.status);
    const checkIn = new Date(reservation.check_in).toLocaleDateString('fr-FR');
    const checkOut = new Date(reservation.check_out).toLocaleDateString('fr-FR');
    const createdAt = new Date(reservation.created_at).toLocaleDateString('fr-FR');

    const detailsHTML = `
        <div class="space-y-4">
            <div class="flex justify-between items-start">
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white">${reservation.hotel_name || 'Hôtel'}</h4>
                    <p class="text-gray-600 dark:text-gray-400">${reservation.room_type || 'Chambre'}</p>
                </div>
                <span class="px-3 py-1 rounded-full text-sm font-medium ${status.class}">
                    ${status.text}
                </span>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Date d'arrivée</p>
                    <p class="font-medium">${checkIn}</p>
                </div>
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Date de départ</p>
                    <p class="font-medium">${checkOut}</p>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Nombre de personnes</p>
                    <p class="font-medium">${reservation.guests || 1}</p>
                </div>
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Nombre de nuits</p>
                    <p class="font-medium">${reservation.nights || 1}</p>
                </div>
            </div>

            <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                <div class="flex justify-between items-center">
                    <span class="text-lg font-semibold text-gray-900 dark:text-white">Prix total</span>
                    <span class="text-xl font-bold text-blue-600 dark:text-blue-400">${reservation.total_price || 0}€</span>
                </div>
            </div>

            <div class="text-sm text-gray-500 dark:text-gray-400">
                <p>Réservation effectuée le ${createdAt}</p>
                ${reservation.confirmation_number ? `<p>Numéro de confirmation: ${reservation.confirmation_number}</p>` : ''}
            </div>
        </div>
    `;

    detailsContainer.innerHTML = detailsHTML;
}

// Close reservation details modal
function closeReservationDetailsModal() {
    const modal = document.getElementById('reservation-details-modal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

// Show form error message
function showFormError(message) {
    const messagesDiv = document.getElementById('edit-profile-messages');
    if (messagesDiv) {
        messagesDiv.innerHTML = `
            <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-md">
                <i class="fas fa-exclamation-circle mr-2"></i>
                ${message}
            </div>
        `;
    }
}

// Show form success message
function showFormSuccess(message) {
    const messagesDiv = document.getElementById('edit-profile-messages');
    if (messagesDiv) {
        messagesDiv.innerHTML = `
            <div class="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-md">
                <i class="fas fa-check-circle mr-2"></i>
                ${message}
            </div>
        `;
    }
}

// Show general error message
function showError(message) {
    // You can implement a toast notification system here
    console.error(message);
    alert(message); // Simple fallback
}

// Show general success message
function showSuccess(message) {
    // You can implement a toast notification system here
    console.log(message);
    alert(message); // Simple fallback
}
