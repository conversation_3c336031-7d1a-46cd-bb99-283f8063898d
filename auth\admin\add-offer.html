<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#ffffff">
    <title>Add New Offer - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script>
        // Tailwind configuration for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400;
        }
        .mobile-nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700;
        }
        .sidebar-link {
            @apply flex items-center px-4 py-3 text-gray-600 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 transition-all duration-300 group relative overflow-hidden;
        }
        .sidebar-link:hover {
            @apply text-blue-700 dark:text-blue-300 shadow-sm transform translate-x-1;
        }
        .sidebar-link.active {
            @apply bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg transform scale-105;
        }
        .sidebar-link.active:hover {
            @apply from-blue-600 to-indigo-700 text-white;
        }
        .sidebar-link i {
            @apply transition-transform duration-300 group-hover:scale-110;
        }
        .sidebar-link.active i {
            @apply text-white;
        }
        .sidebar-link::before {
            @apply absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-blue-500 to-indigo-600 transform scale-y-0 transition-transform duration-300 origin-top;
            content: '';
        }
        .sidebar-link:hover::before {
            @apply scale-y-100;
        }
        .sidebar-link.active::before {
            @apply scale-y-100;
        }
        .logout-link {
            @apply text-red-500 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-300;
        }
        .logout-link::before {
            @apply bg-gradient-to-b from-red-500 to-red-600;
        }
        .card-hover {
            @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
        }
        .sidebar-section {
            @apply space-y-1;
        }
        .sidebar-divider {
            @apply my-6 border-gray-200 dark:border-gray-700 relative;
        }
        .sidebar-divider::after {
            @apply absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-100 dark:bg-gray-800 px-3 text-xs text-gray-500 dark:text-gray-400 font-medium;
            content: 'ACTIONS';
        }
    </style>
    <!-- Load Navbar Script -->
    <script src="/components/load-navbar.js"></script>
</head>
<body class="font-sans antialiased text-gray-800 dark:text-white bg-gray-50 dark:bg-gray-900">
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <!-- Admin Dashboard -->
    <div class="flex flex-col md:flex-row">
        <!-- Sidebar -->
        <aside class="w-full md:w-64 bg-white dark:bg-gray-800 shadow-md md:min-h-screen p-4">
            <div class="text-center mb-8 border-b border-gray-200 dark:border-gray-700 pb-4">
                <div class="h-20 w-20 rounded-full bg-blue-500 dark:bg-blue-600 flex items-center justify-center text-white text-2xl font-bold mx-auto">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h2 class="mt-4 text-xl font-bold text-gray-800 dark:text-white">Admin Dashboard</h2>
                <p class="text-sm text-gray-500 dark:text-gray-400" id="admin-name">Loading...</p>
            </div>

            <nav class="sidebar-section">
                <!-- Main Navigation -->
                <div class="space-y-1 mb-6">
                    <a href="dashboard.html" class="sidebar-link">
                        <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                        <span class="font-medium">Dashboard</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    </a>
                    <a href="manage-offers.html" class="sidebar-link">
                        <i class="fas fa-tag w-5 h-5 mr-3"></i>
                        <span class="font-medium">Manage Offers</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    </a>
                    <a href="manage-reservations.html" class="sidebar-link">
                        <i class="fas fa-calendar-check w-5 h-5 mr-3"></i>
                        <span class="font-medium">Manage Reservations</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    </a>
                    <a href="manage-users.html" class="sidebar-link">
                        <i class="fas fa-users w-5 h-5 mr-3"></i>
                        <span class="font-medium">Manage Users</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    </a>
                </div>

                <!-- Quick Actions -->
                <div class="space-y-1">
                    <div class="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Quick Actions
                    </div>
                    <a href="add-offer.html" class="sidebar-link active" aria-current="page">
                        <i class="fas fa-plus-circle w-5 h-5 mr-3"></i>
                        <span class="font-medium">Add New Offer</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    </a>
                </div>

                <!-- Logout Section -->
                <hr class="sidebar-divider">
                <div class="space-y-1">
                    <a href="#" id="logout-sidebar-btn" class="sidebar-link logout-link">
                        <i class="fas fa-sign-out-alt w-5 h-5 mr-3"></i>
                        <span class="font-medium">Logout</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-4 md:p-8">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Add New Offer</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-2">Create a new travel offer for your customers</p>
            </div>

            <!-- Success/Error Alerts -->
            <div id="success-alert" class="hidden mb-6 bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span id="success-message">Offer created successfully!</span>
                </div>
            </div>

            <div id="error-alert" class="hidden mb-6 bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span id="error-message">An error occurred while creating the offer.</span>
                </div>
            </div>

            <!-- Form Container -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <form id="offerForm" enctype="multipart/form-data" class="space-y-6">
                    <!-- Basic Information Section -->
                    <div class="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="titre" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Title <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="titre" name="titre" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                    placeholder="Enter offer title">
                            </div>
                            <div>
                                <label for="prix" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Price (€) <span class="text-red-500">*</span>
                                </label>
                                <input type="number" id="prix" name="prix" step="0.01" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                    placeholder="0.00">
                            </div>
                        </div>

                        <div class="mt-6">
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Description <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description" name="description" rows="4" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                placeholder="Describe the travel offer in detail..."></textarea>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label for="destination" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Destination <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="destination" name="destination" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                    placeholder="e.g. Paris, France">
                            </div>
                            <div>
                                <label for="point_depart" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Departure Point
                                </label>
                                <input type="text" id="point_depart" name="point_depart"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                    placeholder="e.g. Algiers Airport">
                            </div>
                        </div>
                    </div>

                    <!-- Dates Section -->
                    <div class="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Dates</h3>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="date_debut" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Start Date <span class="text-red-500">*</span>
                                </label>
                                <input type="date" id="date_debut" name="date_debut" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            </div>
                            <div>
                                <label for="date_fin" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    End Date <span class="text-red-500">*</span>
                                </label>
                                <input type="date" id="date_fin" name="date_fin" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            </div>
                            <div>
                                <label for="date_depart" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Departure Date
                                </label>
                                <input type="date" id="date_depart" name="date_depart"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            </div>
                        </div>
                    </div>

                    <!-- Transport & Accommodation Section -->
                    <div class="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Transport & Accommodation</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="type_transport" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Transport Type
                                </label>
                                <input type="text" id="type_transport" name="type_transport"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                    placeholder="e.g. Plane, Bus, Train">
                            </div>
                            <div>
                                <label for="type_hebergement" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Accommodation Type
                                </label>
                                <input type="text" id="type_hebergement" name="type_hebergement"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                    placeholder="e.g. Hotel, Resort, Camping">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                            <div>
                                <label for="classement_hebergement" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Accommodation Stars (1-5)
                                </label>
                                <input type="number" id="classement_hebergement" name="classement_hebergement" min="1" max="5"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="1-5">
                            </div>
                            <div>
                                <label for="repas_inclus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Meals Included
                                </label>
                                <select id="repas_inclus" name="repas_inclus"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <option value="0" selected>No</option>
                                    <option value="1">Yes</option>
                                </select>
                            </div>
                            <div>
                                <label for="activites_incluses" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Activities Included
                                </label>
                                <select id="activites_incluses" name="activites_incluses"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <option value="0" selected>No</option>
                                    <option value="1">Yes</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Capacity & Difficulty Section -->
                    <div class="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Capacity & Difficulty</h3>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="limite_taille_groupe" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Group Size Limit
                                </label>
                                <input type="number" id="limite_taille_groupe" name="limite_taille_groupe" min="1"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="Max people">
                            </div>
                            <div>
                                <label for="places_disponibles" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Available Spots
                                </label>
                                <input type="number" id="places_disponibles" name="places_disponibles" min="0"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="Available spots">
                            </div>
                            <div>
                                <label for="niveau_difficulte" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Difficulty Level
                                </label>
                                <input type="text" id="niveau_difficulte" name="niveau_difficulte"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                    placeholder="e.g. Easy, Moderate, Hard">
                            </div>
                        </div>
                    </div>

                    <!-- Additional Options Section -->
                    <div class="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Additional Options</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="adapte_aux_enfants" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Child Friendly
                                </label>
                                <select id="adapte_aux_enfants" name="adapte_aux_enfants"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <option value="0" selected>No</option>
                                    <option value="1">Yes</option>
                                </select>
                            </div>
                            <div>
                                <label for="est_actif" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Active Offer
                                </label>
                                <select id="est_actif" name="est_actif"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <option value="1" selected>Yes</option>
                                    <option value="0">No</option>
                                </select>
                            </div>
                        </div>

                        <div class="mt-6">
                            <label for="notes_supplementaires" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Additional Notes
                            </label>
                            <textarea id="notes_supplementaires" name="notes_supplementaires" rows="3"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                placeholder="Any additional information about the offer..."></textarea>
                        </div>
                    </div>

                    <!-- Images Section -->
                    <div class="pb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Images</h3>

                        <div>
                            <label for="images" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Upload Images
                            </label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md hover:border-gray-400 dark:hover:border-gray-500 transition-colors duration-200">
                                <div class="space-y-1 text-center">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 dark:text-gray-500"></i>
                                    <div class="flex text-sm text-gray-600 dark:text-gray-400">
                                        <label for="images" class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                            <span>Upload files</span>
                                            <input id="images" name="images[]" type="file" multiple accept="image/*" class="sr-only">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 10MB each</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <button type="submit" id="submit-btn"
                            class="flex-1 sm:flex-none inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            <span id="submit-text">Create Offer</span>
                        </button>
                        <button type="button" onclick="window.location.href='dashboard.html'"
                            class="flex-1 sm:flex-none inline-flex justify-center items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Dashboard
                        </button>
                    </div>
                </form>
            </div>

            <!-- Loading Overlay -->
            <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                    <span class="text-gray-700 dark:text-gray-300">Creating offer...</span>
                </div>
            </div>
        </main>
    </div>

    <!-- Include dashboard authentication script -->
    <script src="dashboard.js"></script>
    <script src="/index/darkmode.js"></script>

    <script>
        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is an admin
            checkAdminAuth();

            // Initialize form
            initializeForm();
        });

        // Function to check if user is logged in and is an admin
        function checkAdminAuth() {
            const token = localStorage.getItem('auth_token');
            const userRole = localStorage.getItem('user_role');

            if (!token) {
                // User is not logged in, redirect to login page
                window.location.href = '../login.html';
                return;
            }

            if (userRole !== 'admin') {
                // User is not an admin, redirect to home page
                alert('Access denied: You do not have admin privileges.');
                window.location.href = '/index/index.html';
                return;
            }

            // User is logged in and is an admin, fetch user data
            fetchUserData();
        }

        // Function to fetch user data and update admin name
        function fetchUserData() {
            const token = localStorage.getItem('auth_token');

            fetch('http://localhost:8000/api/auth/me', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to fetch user data: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('User data fetched:', data);

                // Store updated user data
                localStorage.setItem('user_data', JSON.stringify(data));

                // Update admin name in sidebar
                const name = data.name || (data.data && data.data.name) || 'Admin';
                const adminNameElement = document.getElementById('admin-name');
                if (adminNameElement) {
                    adminNameElement.textContent = name;
                }
            })
            .catch(error => {
                console.error('Error fetching user data:', error);
            });
        }

        // Function to initialize form
        function initializeForm() {
            const form = document.getElementById('offerForm');
            const submitBtn = document.getElementById('submit-btn');
            const submitText = document.getElementById('submit-text');
            const successAlert = document.getElementById('success-alert');
            const errorAlert = document.getElementById('error-alert');
            const successMessage = document.getElementById('success-message');
            const errorMessage = document.getElementById('error-message');
            const loadingOverlay = document.getElementById('loading-overlay');

            // Add logout functionality
            const logoutBtn = document.getElementById('logout-sidebar-btn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    logout();
                });
            }

            // Form submission handler
            form.addEventListener('submit', async (e) => {
                e.preventDefault();

                // Enhanced authentication check
                const authCheck = await validateAuthentication();
                if (!authCheck.valid) {
                    showError(authCheck.error);
                    if (authCheck.redirectToLogin) {
                        setTimeout(() => {
                            window.location.href = '../login.html';
                        }, 2000);
                    }
                    return;
                }

                const token = authCheck.token;

                // Show loading state
                setLoadingState(true);
                hideAlerts();

                const formData = new FormData(form);

                // Fix empty select values - convert empty strings to null for boolean fields
                const booleanFields = ['repas_inclus', 'activites_incluses', 'adapte_aux_enfants'];
                booleanFields.forEach(field => {
                    const value = formData.get(field);
                    if (value === '' || value === null) {
                        formData.delete(field);
                        formData.append(field, '0'); // Default to 0 (No) for boolean fields
                    }
                });

                // Fix empty number fields
                const numberFields = ['classement_hebergement', 'limite_taille_groupe', 'places_disponibles'];
                numberFields.forEach(field => {
                    const value = formData.get(field);
                    if (value === '' || value === null) {
                        formData.delete(field);
                        // Don't append anything - let the server handle null values for optional number fields
                    }
                });

                // Debug: Show what we're sending
                console.log('FormData contents (after processing):');
                for (let [key, value] of formData.entries()) {
                    console.log(key, value);
                }

                // Debug: Show token info
                console.log('Using token:', token.substring(0, 20) + '...');

                try {
                    const response = await fetch('http://localhost:8000/api/offers', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Accept': 'application/json'
                            // Don't set Content-Type for FormData
                        },
                        body: formData
                    });

                    const responseText = await response.text();
                    console.log('Raw response:', responseText);
                    console.log('Response status:', response.status);
                    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                    let result;
                    try {
                        result = JSON.parse(responseText);
                    } catch (e) {
                        result = { error: 'Invalid JSON response', response: responseText };
                    }

                    if (response.ok) {
                        showSuccess(result.message || 'Offer created successfully!');
                        form.reset();

                        // Check if images were uploaded
                        if (result.data && result.data.images) {
                            console.log('Images uploaded:', result.data.images);
                            result.data.images.forEach(img => {
                                console.log(`Image path: ${img.image_path}`);
                            });
                        }

                        // Redirect after 3 seconds
                        setTimeout(() => {
                            window.location.href = 'manage-offers.html';
                        }, 3000);
                    } else {
                        // Handle authentication errors specifically
                        if (response.status === 401) {
                            console.error('Authentication failed - token may be expired or invalid');
                            showError('Authentication failed. Your session may have expired. Please login again.');

                            // Try to refresh token first
                            const refreshResult = await attemptTokenRefresh();
                            if (refreshResult.success) {
                                showError('Token refreshed. Please try submitting the form again.');
                            } else {
                                // Clear auth data and redirect to login
                                localStorage.removeItem('auth_token');
                                localStorage.removeItem('user_role');
                                localStorage.removeItem('user_data');
                                setTimeout(() => {
                                    window.location.href = '../login.html';
                                }, 3000);
                            }
                        } else if (result.errors) {
                            let errorText = result.message || 'Validation failed';
                            if (typeof result.errors === 'object') {
                                const errorList = Object.values(result.errors).flat();
                                errorText += ':\n• ' + errorList.join('\n• ');
                            }
                            showError(errorText);
                        } else {
                            showError(result.message || `Failed to create offer (Status: ${response.status})`);
                        }
                    }

                } catch (error) {
                    console.error('Request failed:', error);
                    showError('Network error: ' + error.message + '. Please check if the backend server is running.');
                } finally {
                    setLoadingState(false);
                }
            });

            // Helper functions
            function setLoadingState(loading) {
                if (loading) {
                    submitBtn.disabled = true;
                    submitText.textContent = 'Creating...';
                    loadingOverlay.classList.remove('hidden');
                } else {
                    submitBtn.disabled = false;
                    submitText.textContent = 'Create Offer';
                    loadingOverlay.classList.add('hidden');
                }
            }

            function showSuccess(message) {
                successMessage.textContent = message;
                successAlert.classList.remove('hidden');
                errorAlert.classList.add('hidden');

                // Scroll to top to show the alert
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            function showError(message) {
                errorMessage.textContent = message;
                errorAlert.classList.remove('hidden');
                successAlert.classList.add('hidden');

                // Scroll to top to show the alert
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            function hideAlerts() {
                successAlert.classList.add('hidden');
                errorAlert.classList.add('hidden');
            }
        }

        // Enhanced authentication validation
        async function validateAuthentication() {
            const token = localStorage.getItem('auth_token');
            const userRole = localStorage.getItem('user_role');

            // Check if token exists
            if (!token) {
                return {
                    valid: false,
                    error: 'No authentication token found. Please login.',
                    redirectToLogin: true
                };
            }

            // Check if user is admin
            if (userRole !== 'admin') {
                return {
                    valid: false,
                    error: 'Access denied. Admin privileges required.',
                    redirectToLogin: true
                };
            }

            // Check token format (JWT should have 3 parts)
            const tokenParts = token.split('.');
            if (tokenParts.length !== 3) {
                return {
                    valid: false,
                    error: 'Invalid token format. Please login again.',
                    redirectToLogin: true
                };
            }

            // Check if token is expired (decode JWT payload)
            try {
                const payload = JSON.parse(atob(tokenParts[1]));
                if (payload.exp) {
                    const expiry = new Date(payload.exp * 1000);
                    const now = new Date();

                    if (expiry < now) {
                        console.log('Token expired at:', expiry);
                        return {
                            valid: false,
                            error: 'Your session has expired. Please login again.',
                            redirectToLogin: true
                        };
                    }
                }
            } catch (error) {
                console.error('Error decoding token:', error);
                return {
                    valid: false,
                    error: 'Invalid token. Please login again.',
                    redirectToLogin: true
                };
            }

            // Test token with backend
            try {
                const response = await fetch('http://localhost:8000/api/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const userData = await response.json();
                    console.log('Token validation successful:', userData);

                    // Update stored user data
                    localStorage.setItem('user_data', JSON.stringify(userData));

                    return {
                        valid: true,
                        token: token,
                        userData: userData
                    };
                } else {
                    console.error('Token validation failed:', response.status);
                    return {
                        valid: false,
                        error: `Authentication failed (${response.status}). Please login again.`,
                        redirectToLogin: true
                    };
                }
            } catch (error) {
                console.error('Network error during token validation:', error);
                return {
                    valid: false,
                    error: 'Cannot verify authentication. Please check your connection and try again.',
                    redirectToLogin: false
                };
            }
        }

        // Attempt to refresh the token
        async function attemptTokenRefresh() {
            const token = localStorage.getItem('auth_token');

            if (!token) {
                return { success: false, error: 'No token to refresh' };
            }

            try {
                const response = await fetch('http://localhost:8000/api/auth/refresh', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.access_token) {
                        // Update the token in localStorage
                        localStorage.setItem('auth_token', data.access_token);

                        // Update expiry if provided
                        if (data.expires_in) {
                            const expiryDate = new Date();
                            expiryDate.setSeconds(expiryDate.getSeconds() + data.expires_in);
                            localStorage.setItem('token_expires_at', expiryDate.toISOString());
                        }

                        console.log('Token refreshed successfully');
                        return { success: true, newToken: data.access_token };
                    }
                }

                return { success: false, error: 'Token refresh failed' };
            } catch (error) {
                console.error('Token refresh error:', error);
                return { success: false, error: error.message };
            }
        }

        // Logout function
        function logout() {
            const token = localStorage.getItem('auth_token');

            if (token) {
                fetch('http://localhost:8000/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                })
                .then(() => {
                    // Clear local storage
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_role');
                    localStorage.removeItem('user_data');

                    // Redirect to login page
                    window.location.href = '../login.html';
                })
                .catch(error => {
                    console.error('Logout error:', error);
                    // Clear local storage anyway
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_role');
                    localStorage.removeItem('user_data');

                    // Redirect to login page
                    window.location.href = '../login.html';
                });
            } else {
                // No token, just redirect
                window.location.href = '../login.html';
            }
        }
    </script>
</body>
</html>
