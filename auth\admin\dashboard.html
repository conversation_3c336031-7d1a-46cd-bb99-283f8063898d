<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#ffffff">
    <title>Admin Dashboard - VoyageGlobe</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script>
        // Tailwind configuration for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400;
        }
        .mobile-nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700;
        }
        .sidebar-link {
            @apply flex items-center px-4 py-2 text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
        }
        .sidebar-link.active {
            @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
        }
        .card-hover {
            @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
        }
    </style>
    <!-- Load Navbar Script -->
    <script src="/components/load-navbar.js"></script>
</head>
<body class="font-sans antialiased text-gray-800 dark:text-white bg-gray-50 dark:bg-gray-900">
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <!-- Admin Dashboard -->
    <div class="flex flex-col md:flex-row">
        <!-- Sidebar -->
        <aside class="w-full md:w-64 bg-white dark:bg-gray-800 shadow-md md:min-h-screen p-4">
            <div class="text-center mb-8 border-b border-gray-200 dark:border-gray-700 pb-4">
                <div class="h-20 w-20 rounded-full bg-blue-500 dark:bg-blue-600 flex items-center justify-center text-white text-2xl font-bold mx-auto">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h2 class="mt-4 text-xl font-bold text-gray-800 dark:text-white">Admin Dashboard</h2>
                <p class="text-sm text-gray-500 dark:text-gray-400" id="admin-name">Loading...</p>
            </div>

            <nav class="space-y-2">
                <a href="dashboard.html" class="sidebar-link active">
                    <i class="fas fa-tachometer-alt w-6"></i>
                    <span class="ml-2">Dashboard</span>
                </a>
                <a href="manage-offers.html" class="sidebar-link">
                    <i class="fas fa-tag w-6"></i>
                    <span class="ml-2">Manage Offers</span>
                </a>
                <a href="manage-reservations.html" class="sidebar-link">
                    <i class="fas fa-calendar-check w-6"></i>
                    <span class="ml-2">Manage Reservations</span>
                </a>
                <a href="manage-users.html" class="sidebar-link">
                    <i class="fas fa-users w-6"></i>
                    <span class="ml-2">Manage Users</span>
                </a>
                <a href="add-offer.html" class="sidebar-link">
                    <i class="fas fa-plus-circle w-6"></i>
                    <span class="ml-2">Add New Offer</span>
                </a>
                <hr class="my-4 border-gray-200 dark:border-gray-700">
                <a href="#" id="logout-sidebar-btn" class="sidebar-link text-red-500 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30">
                    <i class="fas fa-sign-out-alt w-6"></i>
                    <span class="ml-2">Logout</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-4 md:p-8">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Welcome to Admin Dashboard</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-2">Manage your website content, users, and settings</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
                <!-- Total Offers -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300">
                            <i class="fas fa-tag text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Offers</p>
                            <p class="text-2xl font-semibold text-gray-800 dark:text-white" id="total-offers">--</p>
                        </div>
                    </div>
                </div>

                <!-- Active Offers -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300">
                            <i class="fas fa-check-circle text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Offers</p>
                            <p class="text-2xl font-semibold text-gray-800 dark:text-white" id="active-offers">--</p>
                        </div>
                    </div>
                </div>

                <!-- Total Reservations -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300">
                            <i class="fas fa-calendar-check text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Reservations</p>
                            <p class="text-2xl font-semibold text-gray-800 dark:text-white" id="total-reservations">--</p>
                        </div>
                    </div>
                </div>

                <!-- Total Users -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300">
                            <i class="fas fa-users text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Users</p>
                            <p class="text-2xl font-semibold text-gray-800 dark:text-white" id="total-users">--</p>
                        </div>
                    </div>
                </div>

                <!-- New Users (Last 30 days) -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-300">
                            <i class="fas fa-user-plus text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">New Users (30d)</p>
                            <p class="text-2xl font-semibold text-gray-800 dark:text-white" id="new-users">--</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mb-8">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="add-offer.html" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-200">
                        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300">
                            <i class="fas fa-plus-circle text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="font-medium text-gray-800 dark:text-white">Add New Offer</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Create a new travel offer</p>
                        </div>
                    </a>
                    <a href="manage-offers.html" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-200">
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300">
                            <i class="fas fa-edit text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="font-medium text-gray-800 dark:text-white">Manage Offers</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Edit or delete existing offers</p>
                        </div>
                    </a>
                    <a href="manage-reservations.html" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-200">
                        <div class="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300">
                            <i class="fas fa-calendar-check text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="font-medium text-gray-800 dark:text-white">Manage Reservations</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">View and manage bookings</p>
                        </div>
                    </a>
                    <a href="manage-users.html" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-200">
                        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300">
                            <i class="fas fa-user-cog text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="font-medium text-gray-800 dark:text-white">Manage Users</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">View and manage user accounts</p>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Recent Offers -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">Recent Offers</h2>
                    <a href="manage-offers.html" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">View All</a>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Title</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Destination</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Price</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700" id="recent-offers-table">
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                        <div class="flex justify-center items-center">
                                            <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500 mr-3"></div>
                                            Loading recent offers...
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
