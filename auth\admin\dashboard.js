// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in and is an admin
    checkAdminAuth();

    // Initialize dashboard
    initDashboard();

    // Add event listeners
    addEventListeners();
});

// Function to check if user is logged in and is an admin
function checkAdminAuth() {
    const token = localStorage.getItem('auth_token');
    const userRole = localStorage.getItem('user_role');

    if (!token) {
        // User is not logged in, redirect to login page
        window.location.href = '../login.html';
        return;
    }

    if (userRole !== 'admin') {
        // User is not an admin, redirect to home page
        alert('Access denied: You do not have admin privileges.');
        window.location.href = '/index/index.html';
        return;
    }

    // User is logged in and is an admin, fetch user data
    fetchUserData();
}

// Function to fetch user data
function fetchUserData() {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (userData) {
        try {
            const user = JSON.parse(userData);
            const name = user.name || (user.data && user.data.name) || 'Admin';

            // Update admin name in sidebar
            const adminNameElement = document.getElementById('admin-name');
            if (adminNameElement) {
                adminNameElement.textContent = name;
            }
        } catch (error) {
            console.error('Error parsing user data:', error);
        }
    }

    // Refresh user data from API
    fetch('http://localhost:8000/api/auth/me', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch user data: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('User data fetched:', data);

        // Store updated user data
        localStorage.setItem('user_data', JSON.stringify(data));

        // Update admin name in sidebar
        const name = data.name || (data.data && data.data.name) || 'Admin';
        const adminNameElement = document.getElementById('admin-name');
        if (adminNameElement) {
            adminNameElement.textContent = name;
        }
    })
    .catch(error => {
        console.error('Error fetching user data:', error);
    });
}

// Function to initialize dashboard
function initDashboard() {
    // Fetch dashboard stats
    fetchDashboardStats();

    // Fetch recent offers
    fetchRecentOffers();
}

// Function to fetch dashboard stats
function fetchDashboardStats() {
    const token = localStorage.getItem('auth_token');

    // Fetch total offers
    fetch('http://localhost:8000/api/offers', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch offers: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Offers data fetched:', data);

        // Extract offers data
        let offers = [];
        if (data.status === 'success' && data.data) {
            offers = data.data;
        } else if (Array.isArray(data)) {
            offers = data;
        } else if (data.data && Array.isArray(data.data)) {
            offers = data.data;
        }

        // Update total offers count
        const totalOffersElement = document.getElementById('total-offers');
        if (totalOffersElement) {
            totalOffersElement.textContent = offers.length;
        }

        // Update active offers count
        const activeOffers = offers.filter(offer => offer.est_actif === true || offer.est_actif === 1);
        const activeOffersElement = document.getElementById('active-offers');
        if (activeOffersElement) {
            activeOffersElement.textContent = activeOffers.length;
        }
    })
    .catch(error => {
        console.error('Error fetching offers:', error);

        // Set default values on error
        const totalOffersElement = document.getElementById('total-offers');
        const activeOffersElement = document.getElementById('active-offers');

        if (totalOffersElement) totalOffersElement.textContent = '0';
        if (activeOffersElement) activeOffersElement.textContent = '0';
    });

    // Fetch reservations
    fetch('http://localhost:8000/api/reservation-offers', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch reservations: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Reservations data fetched:', data);

        // Extract reservations data
        let reservations = [];
        if (Array.isArray(data)) {
            reservations = data;
        } else if (data.data && Array.isArray(data.data)) {
            reservations = data.data;
        } else if (data.reservations && Array.isArray(data.reservations)) {
            reservations = data.reservations;
        }

        // Update total reservations count
        const totalReservationsElement = document.getElementById('total-reservations');
        if (totalReservationsElement) {
            totalReservationsElement.textContent = reservations.length;
        }
    })
    .catch(error => {
        console.error('Error fetching reservations:', error);

        // Set default value on error
        const totalReservationsElement = document.getElementById('total-reservations');
        if (totalReservationsElement) totalReservationsElement.textContent = '0';
    });

    // Fetch user stats from API
    fetch('http://localhost:8000/api/users', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch users: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Users data fetched for dashboard:', data);

        // Extract users data
        let users = [];
        if (Array.isArray(data)) {
            users = data;
        } else if (data.data && Array.isArray(data.data)) {
            users = data.data;
        } else if (data.users && Array.isArray(data.users)) {
            users = data.users;
        }

        // Update total users count
        const totalUsersElement = document.getElementById('total-users');
        if (totalUsersElement) {
            totalUsersElement.textContent = users.length;
        }

        // Calculate new users (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const newUsers = users.filter(user => {
            const createdDate = new Date(user.created_at);
            return createdDate >= thirtyDaysAgo;
        });

        // Update new users count
        const newUsersElement = document.getElementById('new-users');
        if (newUsersElement) {
            newUsersElement.textContent = newUsers.length;
        }
    })
    .catch(error => {
        console.error('Error fetching users for dashboard:', error);

        // Set default values on error
        const totalUsersElement = document.getElementById('total-users');
        const newUsersElement = document.getElementById('new-users');

        if (totalUsersElement) totalUsersElement.textContent = '0';
        if (newUsersElement) newUsersElement.textContent = '0';
    });
}

// Function to fetch recent offers
function fetchRecentOffers() {
    const token = localStorage.getItem('auth_token');

    fetch('http://localhost:8000/api/offers', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch offers: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Recent offers fetched:', data);

        // Extract offers data
        let offers = [];
        if (data.status === 'success' && data.data) {
            offers = data.data;
        } else if (Array.isArray(data)) {
            offers = data;
        } else if (data.data && Array.isArray(data.data)) {
            offers = data.data;
        }

        // Sort offers by created_at date (newest first)
        offers.sort((a, b) => {
            const dateA = new Date(a.created_at || a.date_debut);
            const dateB = new Date(b.created_at || b.date_debut);
            return dateB - dateA;
        });

        // Take only the 5 most recent offers
        const recentOffers = offers.slice(0, 5);

        // Update recent offers table
        updateRecentOffersTable(recentOffers);
    })
    .catch(error => {
        console.error('Error fetching recent offers:', error);

        // Show error message in table
        const recentOffersTable = document.getElementById('recent-offers-table');
        if (recentOffersTable) {
            recentOffersTable.innerHTML = `
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-red-500 dark:text-red-400">
                        Error loading offers: ${error.message}
                    </td>
                </tr>
            `;
        }
    });
}

// Function to update recent offers table
function updateRecentOffersTable(offers) {
    const recentOffersTable = document.getElementById('recent-offers-table');

    if (!recentOffersTable) return;

    if (offers.length === 0) {
        recentOffersTable.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    No offers found.
                </td>
            </tr>
        `;
        return;
    }

    // Clear table
    recentOffersTable.innerHTML = '';

    // Add offers to table
    offers.forEach(offer => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150';

        // Format date
        const date = new Date(offer.created_at || offer.date_debut);
        const formattedDate = date.toLocaleDateString();

        // Format price
        const price = parseFloat(offer.prix).toLocaleString('fr-FR', {
            style: 'currency',
            currency: 'EUR'
        });

        // Create status badge
        const statusBadge = offer.est_actif === true || offer.est_actif === 1
            ? '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300">Active</span>'
            : '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300">Inactive</span>';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900 dark:text-white">${offer.titre || 'Untitled'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${offer.destination || 'N/A'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${price}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                ${statusBadge}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${formattedDate}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <a href="manage-offers.html?edit=${offer.id}" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3">Edit</a>
                <a href="#" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 delete-offer" data-offer-id="${offer.id}">Delete</a>
            </td>
        `;

        recentOffersTable.appendChild(row);
    });

    // Add event listeners to delete buttons
    document.querySelectorAll('.delete-offer').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const offerId = this.getAttribute('data-offer-id');
            if (confirm('Are you sure you want to delete this offer?')) {
                deleteOffer(offerId);
            }
        });
    });
}

// Function to delete an offer
function deleteOffer(offerId) {
    const token = localStorage.getItem('auth_token');

    fetch(`http://localhost:8000/api/offers/${offerId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to delete offer: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Offer deleted:', data);

        // Refresh dashboard
        initDashboard();

        // Show success message
        alert('Offer deleted successfully!');
    })
    .catch(error => {
        console.error('Error deleting offer:', error);
        alert(`Error deleting offer: ${error.message}`);
    });
}

// Function to add event listeners
function addEventListeners() {
    // No need for add offer button event listener as we're using a direct link

    // Logout button
    const logoutBtn = document.getElementById('logout-sidebar-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// Function to handle logout
function logout() {
    const token = localStorage.getItem('auth_token');

    if (token) {
        // Call logout API
        fetch('http://localhost:8000/api/auth/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            }
        })
        .then(response => {
            // Clear local storage regardless of API response
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_role');

            // Redirect to login page
            window.location.href = '../login.html';
        })
        .catch(error => {
            console.error('Logout error:', error);
            // Still clear local storage and redirect on error
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_role');
            window.location.href = '../login.html';
        });
    } else {
        // If no token, just redirect
        window.location.href = '../login.html';
    }
}
