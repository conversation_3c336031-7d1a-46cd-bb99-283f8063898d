/**
 * Index Page Offers Display
 * Displays only 3 offers from the API on the home page
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, starting to load index offers...');
    console.log('OffersAPI available?', typeof OffersAPI);

    // Wait for all scripts to load
    if (typeof OffersAPI === 'undefined') {
        console.log('OffersAPI not ready, waiting...');
        // Try again after a delay
        setTimeout(() => {
            if (typeof OffersAPI !== 'undefined') {
                loadIndexOffers();
            } else {
                console.error('OffersAPI still not available after delay');
                // Try to load offers with a fallback approach
                loadIndexOffersFallback();
            }
        }, 500);
    } else {
        loadIndexOffers();
    }
});

/**
 * Load and display 3 offers on the index page
 */
async function loadIndexOffers() {
    console.log('loadIndexOffers called');
    const offersContainer = document.getElementById('offers-grid');

    if (!offersContainer) {
        console.error('Offers container not found');
        return;
    }

    console.log('Offers container found:', offersContainer);

    try {
        // Show loading indicator
        showIndexLoading(offersContainer);

        // Check if OffersAPI is available
        if (typeof OffersAPI === 'undefined') {
            throw new Error('OffersAPI class not found. Make sure offers.js is loaded.');
        }

        // Initialize OffersAPI
        const offersAPI = new OffersAPI('http://localhost:8000/api');
        console.log('OffersAPI initialized');

        // Fetch all offers
        console.log('Loading offers for index page...');
        const allOffers = await offersAPI.getAllOffers();
        console.log('Loaded offers:', allOffers);

        // Take only first 3 offers
        const limitedOffers = allOffers.slice(0, 3);
        console.log('Limited to 3 offers:', limitedOffers);

        // Display the offers
        displayIndexOffers(limitedOffers, offersContainer);

    } catch (error) {
        console.error('Error loading offers for index:', error);
        showIndexError(offersContainer, error.message);
    }
}

/**
 * Show loading indicator
 */
function showIndexLoading(container) {
    container.innerHTML = `
        <div class="col-span-full flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <span class="ml-3 text-gray-600 dark:text-gray-400">Chargement des offres...</span>
        </div>
    `;
}

/**
 * Show error message
 */
function showIndexError(container, message) {
    container.innerHTML = `
        <div class="col-span-full">
            <div class="bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
                <p>Erreur lors du chargement des offres: ${message}</p>
            </div>
        </div>
    `;
}

/**
 * Display offers in the index page grid
 */
function displayIndexOffers(offers, container) {
    if (!Array.isArray(offers) || offers.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">Aucune offre disponible</h3>
                <p class="text-gray-500 dark:text-gray-500">Revenez plus tard pour découvrir nos nouvelles offres.</p>
            </div>
        `;
        return;
    }

    // Clear container
    container.innerHTML = '';

    // Create offer cards
    offers.forEach(offer => {
        const offerCard = createIndexOfferCard(offer);
        container.appendChild(offerCard);
    });
}

/**
 * Create offer card for index page
 */
function createIndexOfferCard(offer) {
    const card = document.createElement('div');
    card.className = 'bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transform transition duration-300 hover:scale-105 hover:shadow-xl';

    // Get first image or use placeholder
    const firstImage = offer.images && offer.images.length > 0 ? offer.images[0] : null;
    console.log('Offer:', offer.titre, 'Images:', offer.images, 'First image:', firstImage);
    const imageUrl = firstImage ? getIndexImageUrl(firstImage.image_path) : 'https://via.placeholder.com/400x250?text=No+Image';
    console.log('Generated image URL:', imageUrl);

    // Format price
    const price = offer.prix ? `€${offer.prix}` : 'Prix sur demande';

    // Format dates
    const startDate = offer.date_debut ? new Date(offer.date_debut).toLocaleDateString() : '';
    const endDate = offer.date_fin ? new Date(offer.date_fin).toLocaleDateString() : '';
    const dateRange = startDate && endDate ? `${startDate} - ${endDate}` : 'Dates à définir';

    card.innerHTML = `
        <div class="relative">
            <img src="${imageUrl}" alt="${offer.titre || 'Offre'}"
                 class="w-full h-48 object-cover"
                 onerror="this.src='https://via.placeholder.com/400x250?text=Image+Non+Trouvée'">

            ${offer.images && offer.images.length > 1 ? `
                <div class="absolute top-3 right-3 bg-black bg-opacity-50 text-white px-2 py-1 rounded-full text-sm">
                    <i class="fas fa-images mr-1"></i>${offer.images.length}
                </div>
            ` : ''}

            ${offer.est_actif === 0 ? `
                <div class="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-medium">
                    Inactive
                </div>
            ` : ''}
        </div>

        <div class="p-6">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2 line-clamp-2">
                ${offer.titre || 'Offre sans titre'}
            </h3>

            <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                ${offer.description || 'Aucune description disponible.'}
            </p>

            <div class="space-y-2 mb-4">
                ${offer.destination ? `
                    <div class="flex items-center text-gray-600 dark:text-gray-400">
                        <i class="fas fa-map-marker-alt mr-2 text-blue-500"></i>
                        <span class="text-sm">${offer.destination}</span>
                    </div>
                ` : ''}

                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-calendar-alt mr-2 text-blue-500"></i>
                    <span class="text-sm">${dateRange}</span>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    ${price}
                </div>

                <div class="flex space-x-2">
                    <a href="../offers/offers.html"
                       class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-eye mr-1"></i>Voir plus
                    </a>
                </div>
            </div>
        </div>
    `;

    return card;
}

/**
 * Get image URL from image_path for index page
 */
function getIndexImageUrl(imagePath) {
    // image_path already includes "offers/" directory
    // Example: "offers/KiXWLHfMtJRMC1Qaj7D9861xBv41uLlzjGtmBYHp.jpg"
    // Return HTTP URL that Laravel can serve
    return `http://localhost:8000/storage/${imagePath}`;
}

/**
 * Fallback function to load offers without OffersAPI class
 */
async function loadIndexOffersFallback() {
    const offersContainer = document.getElementById('offers-grid');

    if (!offersContainer) {
        console.error('Offers container not found');
        return;
    }

    try {
        // Show loading indicator
        showIndexLoading(offersContainer);

        // Fetch offers directly
        console.log('Loading offers with fallback method...');
        const response = await fetch('http://localhost:8000/api/offers', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('API Response:', data);

        // Extract offers from response
        let offers;
        if (data.status === 'success' && data.data) {
            offers = data.data;
        } else if (Array.isArray(data)) {
            offers = data;
        } else {
            offers = [];
        }

        console.log('Extracted offers:', offers);

        // Take only first 3 offers
        const limitedOffers = offers.slice(0, 3);
        console.log('Limited to 3 offers:', limitedOffers);

        // Display the offers
        displayIndexOffers(limitedOffers, offersContainer);

    } catch (error) {
        console.error('Error loading offers with fallback:', error);
        showIndexError(offersContainer, error.message);
    }
}
