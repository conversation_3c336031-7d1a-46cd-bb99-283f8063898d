
/* Dark mode styles */
.dark {
    background-color: #1f2937;
    color: #f3f4f6;
}

.dark .bg-white {
    background-color: #374151 !important;
}

.dark .text-gray-800 {
    color: #f3f4f6 !important;
}

.dark .text-gray-900 {
    color: #f9fafb !important;
}

.dark .text-gray-700 {
    color: #e5e7eb !important;
}

.dark .text-gray-600 {
    color: #d1d5db !important;
}

.dark .text-gray-500 {
    color: #9ca3af !important;
}

.dark .text-gray-400 {
    color: #d1d5db !important;
}

.dark .border-gray-300 {
    border-color: #4b5563 !important;
}

.dark .shadow-lg,
.dark .shadow-md,
.dark .shadow-xl {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4) !important;
}

.dark .placeholder-gray-400::placeholder {
    color: #9ca3af !important;
}

/* Dark mode input styles */
.dark input[type="text"],
.dark input[type="email"],
.dark input[type="password"],
.dark input[type="date"],
.dark input[type="search"],
.dark select {
    background-color: #374151 !important;
    color: #f3f4f6 !important;
    border-color: #4b5563 !important;
}

.dark input[type="text"]:focus,
.dark input[type="email"]:focus,
.dark input[type="password"]:focus,
.dark input[type="date"]:focus,
.dark input[type="search"]:focus,
.dark select:focus {
    border-color: #60a5fa !important;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2) !important;
}

.dark .fas.fa-user,
.dark .fas.fa-envelope,
.dark .fas.fa-lock,
.dark .fas.fa-search,
.dark .fas.fa-map-marker-alt,
.dark .fas.fa-calendar {
    color: #9ca3af !important;
}

.dark .search-box {
    background-color: #374151 !important;
}

.dark .bg-gray-50 {
    background-color: #111827 !important;
}

.dark .bg-gray-100 {
    background-color: #1f2937 !important;
}

.dark .bg-gray-800 {
    background-color: #111827 !important;
}

.dark .hover\:bg-gray-50:hover {
    background-color: #374151 !important;
}

.dark .hover\:bg-gray-100:hover {
    background-color: #4b5563 !important;
}

/* Theme toggle animation */
.theme-toggle-icon {
    transition: transform 0.5s ease;
}

.theme-toggle-icon.rotate {
    transform: rotate(360deg);
}

        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --accent-color: #f59e0b;
            --gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes fadeOut {
            to { opacity: 0; visibility: hidden; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @keyframes wave {
            0% { transform: translateX(0); }
            100% { transform: translateX(-25%); }
        }

        [data-animate] {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        [data-animate].visible {
            opacity: 1;
            transform: translateY(0);
        }

        body {
            font-family: 'Poppins', sans-serif;
            scroll-behavior: smooth;
        }

        h1, h2, h3, h4, h5 {
            font-family: 'Playfair Display', serif;
        }

        .hero-gradient {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            position: relative;
        }

        .hero-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1528277342758-f1d7613953a2?ixlib=rb-4.0.3') no-repeat center center;
            background-size: cover;
            opacity: 0.1;
            z-index: 0;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .destination-card {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .destination-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .destination-card .card-image {
            height: 220px;
            transition: transform 0.6s ease;
        }

        .destination-card:hover .card-image {
            transform: scale(1.05);
        }

        .search-box {
            box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.15);
            border-radius: 1rem;
            backdrop-filter: blur(5px);
            background-color: rgba(255, 255, 255, 0.95);
        }

        .hotel-card {
            transition: all 0.3s ease;
            border-radius: 1rem;
            overflow: hidden;
        }

        .hotel-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .hotel-card .hotel-image {
            transition: transform 0.6s ease;
        }

        .hotel-card:hover .hotel-image {
            transform: scale(1.1);
        }

        .parallax {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            position: relative;
        }

        .btn-primary {
            background-color: var(--primary-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(29, 78, 216, 0.3);
        }

        .btn-primary::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .btn-primary:hover::after {
            animation: ripple 1s ease-out;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }

        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(29, 78, 216, 0.3);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 99;
        }

        .back-to-top.visible {
            opacity: 1;
            cursor: pointer;
        }

        .back-to-top:hover {
            background-color: var(--primary-dark);
            transform: translateY(-3px);
        }

        /* Form styles */
        input[type="text"], input[type="email"], input[type="date"] {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        input[type="text"]:focus, input[type="email"]:focus, input[type="date"]:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        /* Rating stars animation */
        .star-rating i {
            transition: transform 0.3s ease;
        }

        .hotel-card:hover .star-rating i {
            transform: scale(1.2);
            transition-delay: calc(0.05s * var(--i));
        }

        /* Custom badge styling */
        .badge {
            padding: 0.35em 0.65em;
            border-radius: 0.5rem;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .badge-discount {
            background-color: var(--accent-color);
            color: white;
        }

        /* Navigation active state */
        .nav-link {
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .nav-link.active::after {
            width: 100%;
        }

        /* Testimonial cards */
        .testimonial-card {
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* Quote styling */
        .testimonial-card p::before {
            content: '"';
            font-size: 2rem;
            line-height: 0;
            vertical-align: -0.4em;
            margin-right: 0.2em;
            color: var(--primary-color);
            opacity: 0.5;
        }

        .testimonial-card p::after {
            content: '"';
            font-size: 2rem;
            line-height: 0;
            vertical-align: -0.4em;
            margin-left: 0.2em;
            color: var(--primary-color);
            opacity: 0.5;
        }
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: fadeOut 1s 2s forwards;
        }

        .loader-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #fff;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

    /* Utility classes */
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }