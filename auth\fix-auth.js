// Authentication Fix Utility
// This script helps diagnose and fix common authentication issues

class AuthFixer {
    constructor() {
        this.apiBase = 'http://localhost:8000/api';
    }

    // Check if token exists and is valid format
    checkTokenFormat() {
        const token = localStorage.getItem('auth_token');
        
        if (!token) {
            return { valid: false, error: 'No token found in localStorage' };
        }

        // Check if it's a JWT token (3 parts separated by dots)
        const parts = token.split('.');
        if (parts.length !== 3) {
            return { valid: false, error: `Invalid JWT format: ${parts.length} parts instead of 3` };
        }

        try {
            // Try to decode the payload
            const payload = JSON.parse(atob(parts[1]));
            
            // Check if token is expired
            if (payload.exp) {
                const expiry = new Date(payload.exp * 1000);
                const now = new Date();
                
                if (expiry < now) {
                    return { valid: false, error: 'Token has expired', expiry: expiry };
                }
            }

            return { 
                valid: true, 
                payload: payload,
                expiry: payload.exp ? new Date(payload.exp * 1000) : null
            };
        } catch (error) {
            return { valid: false, error: `Cannot decode token: ${error.message}` };
        }
    }

    // Test token with the backend
    async testToken() {
        const token = localStorage.getItem('auth_token');
        
        if (!token) {
            return { success: false, error: 'No token available' };
        }

        try {
            const response = await fetch(`${this.apiBase}/auth/me`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            });

            const data = await response.json();

            if (response.ok) {
                return { success: true, data: data };
            } else {
                return { 
                    success: false, 
                    error: data.message || 'Authentication failed',
                    status: response.status,
                    data: data
                };
            }
        } catch (error) {
            return { 
                success: false, 
                error: `Network error: ${error.message}`,
                networkError: true
            };
        }
    }

    // Try to refresh the token
    async refreshToken() {
        const token = localStorage.getItem('auth_token');
        
        if (!token) {
            return { success: false, error: 'No token to refresh' };
        }

        try {
            const response = await fetch(`${this.apiBase}/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            });

            const data = await response.json();

            if (response.ok && data.access_token) {
                // Update the token in localStorage
                localStorage.setItem('auth_token', data.access_token);
                
                // Update expiry if provided
                if (data.expires_in) {
                    const expiryDate = new Date();
                    expiryDate.setSeconds(expiryDate.getSeconds() + data.expires_in);
                    localStorage.setItem('token_expires_at', expiryDate.toISOString());
                }

                return { success: true, newToken: data.access_token };
            } else {
                return { 
                    success: false, 
                    error: data.message || 'Token refresh failed',
                    status: response.status
                };
            }
        } catch (error) {
            return { 
                success: false, 
                error: `Network error: ${error.message}`,
                networkError: true
            };
        }
    }

    // Clear all authentication data
    clearAuth() {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_role');
        localStorage.removeItem('user_data');
        localStorage.removeItem('token_type');
        localStorage.removeItem('token_expires_at');
        
        console.log('All authentication data cleared');
    }

    // Get comprehensive auth status
    async getAuthStatus() {
        const status = {
            hasToken: !!localStorage.getItem('auth_token'),
            tokenFormat: this.checkTokenFormat(),
            userRole: localStorage.getItem('user_role'),
            userData: localStorage.getItem('user_data'),
            tokenExpiry: localStorage.getItem('token_expires_at')
        };

        if (status.hasToken) {
            status.tokenTest = await this.testToken();
        }

        return status;
    }

    // Auto-fix common issues
    async autoFix() {
        console.log('🔧 Starting authentication auto-fix...');
        
        const formatCheck = this.checkTokenFormat();
        
        if (!formatCheck.valid) {
            console.log('❌ Token format invalid:', formatCheck.error);
            
            if (formatCheck.error.includes('expired')) {
                console.log('🔄 Attempting to refresh expired token...');
                const refreshResult = await this.refreshToken();
                
                if (refreshResult.success) {
                    console.log('✅ Token refreshed successfully');
                    return { fixed: true, action: 'token_refreshed' };
                } else {
                    console.log('❌ Token refresh failed:', refreshResult.error);
                    console.log('🧹 Clearing invalid auth data...');
                    this.clearAuth();
                    return { fixed: false, action: 'cleared_auth', reason: 'refresh_failed' };
                }
            } else {
                console.log('🧹 Clearing invalid token...');
                this.clearAuth();
                return { fixed: false, action: 'cleared_auth', reason: 'invalid_format' };
            }
        }

        // Test if token works with backend
        console.log('🧪 Testing token with backend...');
        const tokenTest = await this.testToken();
        
        if (!tokenTest.success) {
            if (tokenTest.networkError) {
                console.log('🌐 Network error - backend might be down');
                return { fixed: false, action: 'network_error', reason: tokenTest.error };
            }
            
            console.log('❌ Token rejected by backend:', tokenTest.error);
            
            // Try refreshing first
            console.log('🔄 Attempting to refresh token...');
            const refreshResult = await this.refreshToken();
            
            if (refreshResult.success) {
                console.log('✅ Token refreshed successfully');
                return { fixed: true, action: 'token_refreshed' };
            } else {
                console.log('❌ Token refresh failed, clearing auth data...');
                this.clearAuth();
                return { fixed: false, action: 'cleared_auth', reason: 'backend_rejected' };
            }
        }

        console.log('✅ Authentication is working correctly');
        return { fixed: true, action: 'no_action_needed' };
    }
}

// Make it available globally
window.AuthFixer = AuthFixer;

// Auto-run diagnostics if this script is loaded directly
if (typeof window !== 'undefined') {
    window.authFixer = new AuthFixer();
    
    // Add console helper functions
    window.checkAuth = async () => {
        const status = await window.authFixer.getAuthStatus();
        console.log('Authentication Status:', status);
        return status;
    };
    
    window.fixAuth = async () => {
        const result = await window.authFixer.autoFix();
        console.log('Auto-fix Result:', result);
        return result;
    };
    
    window.clearAuth = () => {
        window.authFixer.clearAuth();
        console.log('Authentication data cleared. Please login again.');
    };
    
    console.log('🔧 Auth Fixer loaded. Available commands:');
    console.log('  - checkAuth() - Check current authentication status');
    console.log('  - fixAuth() - Attempt to automatically fix auth issues');
    console.log('  - clearAuth() - Clear all authentication data');
}
