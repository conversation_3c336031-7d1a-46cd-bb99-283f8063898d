<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile - AgencyMo</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* Modern Dark Mode Variables */
        :root {
            --bg-light: #ffffff;
            --bg-light-secondary: #f8fafc;
            --bg-light-tertiary: #f1f5f9;
            --text-light: #0f172a;
            --text-light-secondary: #475569;
            --text-light-muted: #64748b;
            --border-light: #e2e8f0;
            --accent-light: #3b82f6;
        }

        .dark {
            --bg-dark: #0a0a0a;
            --bg-dark-secondary: #111111;
            --bg-dark-tertiary: #1a1a1a;
            --bg-dark-card: #161616;
            --bg-dark-elevated: #1f1f1f;
            --text-dark: #ffffff;
            --text-dark-secondary: #a1a1aa;
            --text-dark-muted: #71717a;
            --border-dark: #262626;
            --border-dark-subtle: #1a1a1a;
            --accent-dark: #60a5fa;
            --accent-dark-glow: rgba(96, 165, 250, 0.15);
            --shadow-dark: rgba(0, 0, 0, 0.5);
        }

        /* Enhanced transitions */
        * {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Modern dark mode body */
        .dark body {
            background: var(--bg-dark);
            color: var(--text-dark);
        }

        /* Glassmorphism effect for cards */
        .glass-card {
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .dark .glass-card {
            background: rgba(22, 22, 22, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* Modern gradient backgrounds */
        .dark .gradient-bg {
            background: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
        }

        .dark .gradient-header {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 50%, #1e3a8a 100%);
            position: relative;
        }

        .dark .gradient-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(96, 165, 250, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
            pointer-events: none;
        }

        /* Enhanced button styles */
        .dark .btn-modern {
            background: var(--bg-dark-elevated);
            border: 1px solid var(--border-dark);
            color: var(--text-dark);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .dark .btn-modern:hover {
            background: var(--bg-dark-tertiary);
            border-color: var(--accent-dark);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25), 0 0 0 1px var(--accent-dark-glow);
            transform: translateY(-1px);
        }

        .dark .btn-primary-modern {
            background: linear-gradient(135deg, var(--accent-dark) 0%, #3b82f6 100%);
            border: none;
            color: white;
            box-shadow: 0 4px 12px var(--accent-dark-glow);
        }

        .dark .btn-primary-modern:hover {
            box-shadow: 0 8px 25px var(--accent-dark-glow), 0 0 0 1px var(--accent-dark);
            transform: translateY(-1px);
        }

        /* Modern card styles */
        .dark .card-modern {
            background: var(--bg-dark-card);
            border: 1px solid var(--border-dark-subtle);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .dark .card-modern:hover {
            border-color: var(--border-dark);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
            transform: translateY(-2px);
        }

        /* Enhanced text colors */
        .dark .text-primary-modern {
            color: var(--text-dark);
        }

        .dark .text-secondary-modern {
            color: var(--text-dark-secondary);
        }

        .dark .text-muted-modern {
            color: var(--text-dark-muted);
        }

        /* Modern input styles */
        .dark .input-modern {
            background: var(--bg-dark-elevated);
            border: 1px solid var(--border-dark);
            color: var(--text-dark);
        }

        .dark .input-modern:focus {
            border-color: var(--accent-dark);
            box-shadow: 0 0 0 3px var(--accent-dark-glow);
            background: var(--bg-dark-card);
        }

        /* Glow effects */
        .dark .glow-accent {
            box-shadow: 0 0 20px var(--accent-dark-glow);
        }

        /* Status badges with modern styling */
        .dark .badge-modern {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Legacy compatibility overrides */
        .dark .bg-white { background: var(--bg-dark-card) !important; }
        .dark .bg-gray-50 { background: var(--bg-dark-secondary) !important; }
        .dark .bg-gray-100 { background: var(--bg-dark) !important; }
        .dark .bg-gray-800 { background: var(--bg-dark-card) !important; }
        .dark .bg-gray-900 { background: var(--bg-dark) !important; }

        .dark .text-gray-900 { color: var(--text-dark) !important; }
        .dark .text-gray-800 { color: var(--text-dark) !important; }
        .dark .text-gray-700 { color: var(--text-dark-secondary) !important; }
        .dark .text-gray-600 { color: var(--text-dark-secondary) !important; }
        .dark .text-gray-500 { color: var(--text-dark-muted) !important; }
        .dark .text-gray-400 { color: var(--text-dark-muted) !important; }

        .dark .border-gray-200 { border-color: var(--border-dark-subtle) !important; }
        .dark .border-gray-300 { border-color: var(--border-dark) !important; }
        .dark .border-gray-600 { border-color: var(--border-dark) !important; }
        .dark .border-gray-700 { border-color: var(--border-dark-subtle) !important; }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Test Controls -->
    <div class="bg-yellow-100 dark:bg-yellow-900/20 backdrop-blur-sm p-6 text-center border-b border-yellow-200 dark:border-yellow-800">
        <h2 class="text-2xl font-bold text-yellow-800 dark:text-yellow-200 mb-3">🧪 Modern Profile Test Page</h2>
        <p class="text-yellow-700 dark:text-yellow-300 mb-6">Experience the new Al Zeb-inspired dark mode design</p>
        <div class="flex flex-wrap justify-center gap-4">
            <button onclick="setupTestData()" class="btn-primary-modern px-6 py-3 rounded-xl font-semibold glow-accent">
                <i class="fas fa-database mr-2"></i> Setup Test Data
            </button>
            <button onclick="clearTestData()" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all">
                <i class="fas fa-trash mr-2"></i> Clear Test Data
            </button>
            <button onclick="toggleTestDarkMode()" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all">
                <i class="fas fa-moon mr-2"></i> Toggle Dark Mode
            </button>
            <button onclick="window.location.reload()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all">
                <i class="fas fa-refresh mr-2"></i> Reload Page
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="glass-card rounded-2xl shadow-2xl overflow-hidden">
            <!-- Profile Header -->
            <div class="gradient-header p-8 text-white relative">
                <div class="flex flex-col md:flex-row items-center">
                    <div class="h-24 w-24 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center text-blue-600 dark:text-blue-400 text-3xl font-bold mb-4 md:mb-0 md:mr-6">
                        <span id="user-initials">U</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold" id="user-name">Loading...</h1>
                        <p class="text-blue-200 dark:text-blue-300" id="user-email">Loading...</p>
                        <p class="text-blue-200 dark:text-blue-300 mt-1" id="user-role">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Profile Content -->
            <div class="p-6">
                <!-- Tabs -->
                <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
                    <nav class="flex space-x-8">
                        <button id="tab-profile" class="tab-button border-b-2 border-blue-500 dark:border-blue-400 px-1 py-4 text-sm font-medium text-blue-600 dark:text-blue-400">
                            <i class="fas fa-user mr-2"></i> Mon Profil
                        </button>
                        <button id="tab-reservations" class="tab-button border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-calendar-check mr-2"></i> Mes Réservations
                        </button>
                    </nav>
                </div>

                <!-- Profile Tab Content -->
                <div id="profile-content" class="tab-content">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-6">
                        <h2 class="text-xl font-bold mb-4">Informations Personnelles</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">Nom</p>
                                <p class="font-medium" id="profile-name">Loading...</p>
                            </div>
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">Email</p>
                                <p class="font-medium" id="profile-email">Loading...</p>
                            </div>
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">Téléphone</p>
                                <p class="font-medium" id="profile-phone">Non spécifié</p>
                            </div>
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">Date d'inscription</p>
                                <p class="font-medium" id="profile-created">Loading...</p>
                            </div>
                        </div>
                        <div class="mt-6">
                            <button id="edit-profile-btn" class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                <i class="fas fa-edit mr-2"></i> Modifier mes informations
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Reservations Tab Content -->
                <div id="reservations-content" class="tab-content hidden">
                    <div class="mb-6">
                        <h2 class="text-xl font-bold mb-4">Mes Réservations</h2>
                        <div id="reservations-container" class="space-y-4">
                            <div class="flex justify-center items-center py-8">
                                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mr-3"></div>
                                <span>Chargement des réservations...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div id="edit-profile-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6 shadow-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Modifier mon profil</h3>
                <button id="close-edit-modal" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="edit-profile-form" class="space-y-4">
                <div>
                    <label for="edit-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nom</label>
                    <input type="text" id="edit-name" name="name" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label for="edit-email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
                    <input type="email" id="edit-email" name="email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label for="edit-phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Téléphone</label>
                    <input type="text" id="edit-phone" name="phone" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div id="edit-profile-messages"></div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" id="cancel-edit-btn" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Annuler
                    </button>
                    <button type="submit" id="save-profile-btn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Reservation Details Modal -->
    <div id="reservation-details-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full p-6 shadow-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Détails de la réservation</h3>
                <button id="close-reservation-modal" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="reservation-details" class="space-y-4">
                <!-- Reservation details will be inserted here by JavaScript -->
                <div class="flex justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="profile.js"></script>
    <script>
        // Initialize dark mode for test page
        function initializeTestDarkMode() {
            if (localStorage.getItem('color-theme') === 'dark' ||
                (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }

        // Initialize dark mode immediately
        initializeTestDarkMode();
    </script>
    <script>
        // Dark mode toggle function for test page
        function toggleTestDarkMode() {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('color-theme', 'light');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('color-theme', 'dark');
            }
        }

        // Test functions
        function setupTestData() {
            // Setup test user data
            const testUserData = {
                status: 'success',
                data: {
                    id: 1,
                    name: 'Jean Dupont',
                    email: '<EMAIL>',
                    phone: '+33 6 12 34 56 78',
                    role: 'client',
                    created_at: '2024-01-15T10:30:00Z'
                }
            };

            // Setup test auth data
            localStorage.setItem('auth_token', 'test_token_123456789');
            localStorage.setItem('user_data', JSON.stringify(testUserData));
            localStorage.setItem('user_role', 'client');
            localStorage.setItem('token_expires_at', new Date(Date.now() + 3600000).toISOString()); // 1 hour from now

            alert('✅ Test data has been set up! The page will reload to show the profile with sample data.');
            window.location.reload();
        }

        function clearTestData() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_role');
            localStorage.removeItem('token_expires_at');

            alert('🗑️ Test data has been cleared!');
            window.location.reload();
        }

        // Override API calls for testing
        window.fetch = function(url, options) {
            console.log('Mock API call to:', url, options);

            if (url.includes('/api/auth/me')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        status: 'success',
                        data: {
                            id: 1,
                            name: 'Jean Dupont',
                            email: '<EMAIL>',
                            phone: '+33 6 12 34 56 78',
                            role: 'client',
                            created_at: '2024-01-15T10:30:00Z'
                        }
                    })
                });
            }

            if (url.includes('/api/auth/profile') && options.method === 'PUT') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        status: 'success',
                        message: 'Profile updated successfully'
                    })
                });
            }

            if (url.includes('/api/reservations/user')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        status: 'success',
                        data: [
                            {
                                id: 1,
                                hotel_name: 'Hôtel Paradise',
                                room_type: 'Chambre Deluxe',
                                check_in: '2024-02-15',
                                check_out: '2024-02-20',
                                guests: 2,
                                nights: 5,
                                total_price: 750,
                                status: 'confirmed',
                                created_at: '2024-01-20T14:30:00Z',
                                confirmation_number: 'HP2024001'
                            },
                            {
                                id: 2,
                                hotel_name: 'Resort Tropical',
                                room_type: 'Suite Ocean View',
                                check_in: '2024-03-10',
                                check_out: '2024-03-15',
                                guests: 4,
                                nights: 5,
                                total_price: 1200,
                                status: 'pending',
                                created_at: '2024-01-25T09:15:00Z'
                            }
                        ]
                    })
                });
            }

            if (url.includes('/api/reservations/')) {
                const reservationId = url.split('/').pop();
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        status: 'success',
                        data: {
                            id: parseInt(reservationId),
                            hotel_name: 'Hôtel Paradise',
                            room_type: 'Chambre Deluxe',
                            check_in: '2024-02-15',
                            check_out: '2024-02-20',
                            guests: 2,
                            nights: 5,
                            total_price: 750,
                            status: 'confirmed',
                            created_at: '2024-01-20T14:30:00Z',
                            confirmation_number: 'HP2024001'
                        }
                    })
                });
            }

            return Promise.reject(new Error('Mock API: Endpoint not found'));
        };
    </script>
</body>
</html>
