<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Debug Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">
                <i class="fas fa-bug mr-2 text-red-500"></i>
                Authentication Debug Tool
            </h1>

            <!-- Current Auth Status -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">Current Authentication Status</h2>
                <div id="auth-status" class="space-y-2"></div>
            </div>

            <!-- Token Information -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">Token Information</h2>
                <div id="token-info" class="space-y-2"></div>
            </div>

            <!-- API Test Results -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">API Test Results</h2>
                <div class="space-y-4">
                    <button id="test-me-endpoint" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-user mr-2"></i>Test /api/auth/me
                    </button>
                    <button id="test-offers-endpoint" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded ml-2">
                        <i class="fas fa-tag mr-2"></i>Test /api/offers
                    </button>
                    <button id="refresh-token" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded ml-2">
                        <i class="fas fa-refresh mr-2"></i>Refresh Token
                    </button>
                </div>
                <div id="api-results" class="mt-4 space-y-2"></div>
            </div>

            <!-- Actions -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">Actions</h2>
                <div class="space-x-2">
                    <button id="clear-storage" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-trash mr-2"></i>Clear All Auth Data
                    </button>
                    <button id="goto-login" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-sign-in-alt mr-2"></i>Go to Login
                    </button>
                </div>
            </div>

            <!-- Raw Data -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">Raw LocalStorage Data</h2>
                <div id="raw-data" class="bg-gray-100 p-4 rounded text-sm font-mono overflow-auto max-h-96"></div>
            </div>
        </div>
    </div>

    <script>
        // Initialize debug tool
        document.addEventListener('DOMContentLoaded', function() {
            displayAuthStatus();
            displayTokenInfo();
            displayRawData();
            setupEventListeners();
        });

        function displayAuthStatus() {
            const statusDiv = document.getElementById('auth-status');
            const token = localStorage.getItem('auth_token');
            const userRole = localStorage.getItem('user_role');
            const userData = localStorage.getItem('user_data');
            const tokenExpiry = localStorage.getItem('token_expires_at');

            let html = '';

            // Token existence
            html += `<div class="flex items-center">
                <span class="w-4 h-4 rounded-full ${token ? 'bg-green-500' : 'bg-red-500'} mr-2"></span>
                <span>Auth Token: ${token ? 'Present' : 'Missing'}</span>
            </div>`;

            // User role
            html += `<div class="flex items-center">
                <span class="w-4 h-4 rounded-full ${userRole ? 'bg-green-500' : 'bg-red-500'} mr-2"></span>
                <span>User Role: ${userRole || 'Not set'}</span>
            </div>`;

            // User data
            html += `<div class="flex items-center">
                <span class="w-4 h-4 rounded-full ${userData ? 'bg-green-500' : 'bg-red-500'} mr-2"></span>
                <span>User Data: ${userData ? 'Present' : 'Missing'}</span>
            </div>`;

            // Token expiry
            if (tokenExpiry) {
                const expiryDate = new Date(tokenExpiry);
                const now = new Date();
                const isExpired = expiryDate < now;
                html += `<div class="flex items-center">
                    <span class="w-4 h-4 rounded-full ${isExpired ? 'bg-red-500' : 'bg-green-500'} mr-2"></span>
                    <span>Token Expiry: ${expiryDate.toLocaleString()} ${isExpired ? '(EXPIRED)' : '(Valid)'}</span>
                </div>`;
            }

            statusDiv.innerHTML = html;
        }

        function displayTokenInfo() {
            const tokenDiv = document.getElementById('token-info');
            const token = localStorage.getItem('auth_token');

            if (!token) {
                tokenDiv.innerHTML = '<p class="text-red-500">No token found</p>';
                return;
            }

            let html = `<div class="space-y-2">`;
            
            // Token preview
            html += `<div><strong>Token Preview:</strong> ${token.substring(0, 50)}...</div>`;
            
            // Token length
            html += `<div><strong>Token Length:</strong> ${token.length} characters</div>`;

            // Try to decode JWT
            try {
                const parts = token.split('.');
                if (parts.length === 3) {
                    const header = JSON.parse(atob(parts[0]));
                    const payload = JSON.parse(atob(parts[1]));
                    
                    html += `<div><strong>JWT Header:</strong> <pre class="bg-gray-100 p-2 rounded text-xs">${JSON.stringify(header, null, 2)}</pre></div>`;
                    html += `<div><strong>JWT Payload:</strong> <pre class="bg-gray-100 p-2 rounded text-xs">${JSON.stringify(payload, null, 2)}</pre></div>`;
                    
                    // Check expiry
                    if (payload.exp) {
                        const expiry = new Date(payload.exp * 1000);
                        const now = new Date();
                        const isExpired = expiry < now;
                        html += `<div><strong>JWT Expiry:</strong> ${expiry.toLocaleString()} ${isExpired ? '(EXPIRED)' : '(Valid)'}</div>`;
                    }
                } else {
                    html += `<div class="text-red-500">Invalid JWT format (${parts.length} parts instead of 3)</div>`;
                }
            } catch (error) {
                html += `<div class="text-red-500">Error decoding JWT: ${error.message}</div>`;
            }

            html += `</div>`;
            tokenDiv.innerHTML = html;
        }

        function displayRawData() {
            const rawDiv = document.getElementById('raw-data');
            const authData = {
                auth_token: localStorage.getItem('auth_token'),
                user_role: localStorage.getItem('user_role'),
                user_data: localStorage.getItem('user_data'),
                token_type: localStorage.getItem('token_type'),
                token_expires_at: localStorage.getItem('token_expires_at')
            };

            rawDiv.textContent = JSON.stringify(authData, null, 2);
        }

        function setupEventListeners() {
            // Test /me endpoint
            document.getElementById('test-me-endpoint').addEventListener('click', async function() {
                await testEndpoint('/api/auth/me', 'GET');
            });

            // Test /offers endpoint
            document.getElementById('test-offers-endpoint').addEventListener('click', async function() {
                await testEndpoint('/api/offers', 'GET');
            });

            // Refresh token
            document.getElementById('refresh-token').addEventListener('click', async function() {
                await testEndpoint('/api/auth/refresh', 'POST');
            });

            // Clear storage
            document.getElementById('clear-storage').addEventListener('click', function() {
                if (confirm('Are you sure you want to clear all authentication data?')) {
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_role');
                    localStorage.removeItem('user_data');
                    localStorage.removeItem('token_type');
                    localStorage.removeItem('token_expires_at');
                    location.reload();
                }
            });

            // Go to login
            document.getElementById('goto-login').addEventListener('click', function() {
                window.location.href = 'login.html';
            });
        }

        async function testEndpoint(endpoint, method = 'GET') {
            const resultsDiv = document.getElementById('api-results');
            const token = localStorage.getItem('auth_token');

            if (!token) {
                addResult('No token available for testing', 'error');
                return;
            }

            addResult(`Testing ${method} ${endpoint}...`, 'info');

            try {
                const response = await fetch(`http://localhost:8000${endpoint}`, {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }

                if (response.ok) {
                    addResult(`✅ ${endpoint}: ${response.status} ${response.statusText}`, 'success');
                    addResult(`Response: ${JSON.stringify(responseData, null, 2)}`, 'success');
                    
                    // If this is a refresh token call and successful, update the token
                    if (endpoint === '/api/auth/refresh' && responseData.access_token) {
                        localStorage.setItem('auth_token', responseData.access_token);
                        addResult('Token refreshed successfully!', 'success');
                        setTimeout(() => location.reload(), 1000);
                    }
                } else {
                    addResult(`❌ ${endpoint}: ${response.status} ${response.statusText}`, 'error');
                    addResult(`Error: ${JSON.stringify(responseData, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ ${endpoint}: Network Error - ${error.message}`, 'error');
            }
        }

        function addResult(message, type) {
            const resultsDiv = document.getElementById('api-results');
            const div = document.createElement('div');
            div.className = `p-2 rounded text-sm ${
                type === 'success' ? 'bg-green-100 text-green-800' :
                type === 'error' ? 'bg-red-100 text-red-800' :
                'bg-blue-100 text-blue-800'
            }`;
            div.innerHTML = `<pre class="whitespace-pre-wrap">${message}</pre>`;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
    </script>
</body>
</html>
