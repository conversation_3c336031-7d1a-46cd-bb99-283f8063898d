
// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is already logged in
    const token = localStorage.getItem('auth_token');
    if (token) {
        // Redirect to appropriate page based on role
        const userRole = localStorage.getItem('user_role');
        if (userRole === 'admin') {
            window.location.href = 'admin/dashboard.html';
        } else {
            window.location.href = '../index/index.html';
        }
    }

    // Animate staggered items
    const staggerItems = document.querySelectorAll('.stagger-item');
    staggerItems.forEach(item => {
        const delay = item.getAttribute('style');
        if (delay) {
            setTimeout(() => {
                item.style.opacity = '1';
            }, parseInt(delay.match(/\d+/)[0]) * 1000);
        }
    });

    // Animate float-in items
    const floatItems = document.querySelectorAll('.float-in');
    floatItems.forEach(item => {
        const delay = item.getAttribute('style');
        if (delay) {
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, parseInt(delay.match(/\d+/)[0]) * 1000);
        }
    });
});

// Création des particules d'arrière-plan
function createParticles() {
    const particles = document.getElementById('particles');
    const colors = ['#3b82f6', '#60a5fa', '#93c5fd'];

    for (let i = 0; i < 30; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // Taille aléatoire
        const size = Math.random() * 20 + 5;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        // Position aléatoire
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;

        // Couleur aléatoire
        particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];

        // Animation delay aléatoire
        particle.style.animationDelay = `${Math.random() * 5}s`;

        particles.appendChild(particle);
    }
}

// Effet de focus sur les champs
const inputs = document.querySelectorAll('input[type="email"], input[type="password"]');
inputs.forEach(input => {
    input.addEventListener('focus', () => {
        input.parentElement.classList.add('ring-2', 'ring-blue-200', 'ring-opacity-50');

        // Cacher le message d'erreur quand l'utilisateur commence à taper
        const errorContainer = document.getElementById('login-error');
        if (errorContainer) {
            errorContainer.classList.add('hidden');
        }
    });

    input.addEventListener('blur', () => {
        input.parentElement.classList.remove('ring-2', 'ring-blue-200', 'ring-opacity-50');
    });

    // Cacher également le message d'erreur quand l'utilisateur tape
    input.addEventListener('input', () => {
        const errorContainer = document.getElementById('login-error');
        if (errorContainer) {
            errorContainer.classList.add('hidden');
        }
    });
});

// Animation du bouton de connexion
document.getElementById('login-button').addEventListener('mouseover', function() {
    this.classList.add('animate__animated', 'animate__pulse');
});

document.getElementById('login-button').addEventListener('mouseout', function() {
    this.classList.remove('animate__animated', 'animate__pulse');
});

// Form validation and submission
const loginForm = document.getElementById('login-form');
if (loginForm) {
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const errorContainer = document.getElementById('login-error');
        const errorMessage = document.getElementById('error-message');

        // Basic validation
        if (!email || !email.includes('@')) {
            showError('Please enter a valid email address');
            return;
        }

        if (!password || password.length < 6) {
            showError('Password must be at least 6 characters long');
            return;
        }

        // Show loading state
        const loginButton = document.getElementById('login-button');
        const originalButtonText = loginButton.innerHTML;
        loginButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Signing in...';
        loginButton.disabled = true;

        // Call the login API
        fetch('http://localhost:8000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw new Error(errorData.message || 'Invalid credentials');
                }).catch(err => {
                    if (err instanceof SyntaxError) {
                        if (response.status === 401) {
                            throw new Error('Invalid credentials');
                        } else if (response.status === 422) {
                            throw new Error('Invalid form data');
                        } else {
                            throw new Error(`Server error (${response.status})`);
                        }
                    }
                    throw err;
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('Login successful:', data);

            // Extract token and user data
            const { token, userData, userRole } = extractAuthData(data);

            // Store auth data in localStorage
            storeAuthData(token, userData, userRole);

            // Show success message and redirect
            showSuccess('Login successful! Redirecting...');

            // Add exit animation
            document.querySelector('.max-w-md').classList.add('animate__animated', 'animate__fadeOutUp');

            // Redirect based on role
            setTimeout(() => {
                if (userRole === 'admin') {
                    window.location.href = 'admin/dashboard.html';
                } else {
                    window.location.href = '../index/index.html';
                }
            }, 1500);
        })
        .catch(error => {
            console.error('Login error:', error);
            showError(error.message || 'Failed to login');

            // Reset button
            loginButton.innerHTML = originalButtonText;
            loginButton.disabled = false;
        });
    });
}

// Extract token and user data from API response
function extractAuthData(responseData) {
    console.log('API response:', responseData);

    let token = '';
    let userData = {};
    let userRole = 'client'; // Default role

    // Extract token based on response structure
    if (responseData.access_token) {
        token = responseData.access_token;
    } else if (responseData.token) {
        token = responseData.token;
    } else if (responseData.data && responseData.data.access_token) {
        token = responseData.data.access_token;
    } else if (responseData.data && responseData.data.token) {
        token = responseData.data.token;
    }

    // Extract user data
    if (responseData.user) {
        userData = responseData.user;
    } else if (responseData.data && responseData.data.user) {
        userData = responseData.data.user;
    }

    // Extract role
    if (userData.role) {
        userRole = userData.role.toLowerCase();
    } else if (token) {
        // Try to extract role from JWT token
        try {
            const tokenParts = token.split('.');
            if (tokenParts.length === 3) {
                const payload = JSON.parse(atob(tokenParts[1]));
                if (payload.role) {
                    userRole = payload.role.toLowerCase();
                    // Add role to user data
                    userData.role = payload.role;
                }
            }
        } catch (error) {
            console.error('Error extracting data from JWT:', error);
        }
    }

    // If no user data found, try to get it from the API
    if (Object.keys(userData).length === 0 && token) {
        // We'll fetch user data after storing the token
        console.log('No user data in response, will fetch from /me endpoint');
    }

    return { token, userData, userRole };
}

// Store authentication data in localStorage
function storeAuthData(token, userData, userRole) {
    // Store token
    localStorage.setItem('auth_token', token);
    localStorage.setItem('token_type', 'bearer');

    // Calculate token expiration (default: 1 hour)
    const expirationDate = new Date();
    expirationDate.setHours(expirationDate.getHours() + 1);
    localStorage.setItem('token_expires_at', expirationDate.toISOString());

    // Store user data
    const userDataToStore = {
        status: 'success',
        data: userData
    };
    localStorage.setItem('user_data', JSON.stringify(userDataToStore));

    // Store user role
    localStorage.setItem('user_role', userRole);

    console.log('Auth data stored:', {
        token: token.substring(0, 10) + '...',
        userData,
        userRole
    });

    // If user data is empty, fetch it from the API
    if (Object.keys(userData).length === 0) {
        fetchUserData(token);
    }
}

// Fetch user data from the API
function fetchUserData(token) {
    fetch('http://localhost:8000/api/auth/me', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch user data: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('User data fetched:', data);

        // Extract role
        let userRole = 'client';
        if (data.role) {
            userRole = data.role.toLowerCase();
        } else if (data.data && data.data.role) {
            userRole = data.data.role.toLowerCase();
        }

        // Store updated user data
        localStorage.setItem('user_data', JSON.stringify(data));
        localStorage.setItem('user_role', userRole);
    })
    .catch(error => {
        console.error('Error fetching user data:', error);
    });
}

// Show error message
function showError(message) {
    const errorContainer = document.getElementById('login-error');
    const errorMessage = document.getElementById('error-message');

    if (errorContainer && errorMessage) {
        errorMessage.textContent = message;
        errorContainer.classList.remove('hidden');
    }
}

// Show success message
function showSuccess(message) {
    // Create a success notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 bg-green-500 text-white animate__animated animate__fadeInDown';

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <p>${message}</p>
        </div>
    `;

    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('animate__fadeInDown');
        notification.classList.add('animate__fadeOutUp');
        setTimeout(() => {
            notification.remove();
        }, 1000);
    }, 3000);
}

// Add test credentials button (for development only)
window.addEventListener('load', () => {
    // Function to pre-fill test credentials
    const fillTestCredentials = () => {
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('password').value = 'password';
    };

    // Add a discreet button to fill test credentials
    const testButton = document.createElement('button');
    testButton.type = 'button';
    testButton.className = 'text-xs text-gray-400 hover:text-gray-600 absolute bottom-2 right-2 opacity-50';
    testButton.textContent = 'Test';
    testButton.addEventListener('click', fillTestCredentials);

    const container = document.querySelector('.max-w-md');
    if (container) {
        container.appendChild(testButton);
    }
});
