/**
 * Destinations API Integration for index page
 * Fetches destinations from offers API and displays popular destinations
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Loading destinations...');
    loadDestinations();
});

/**
 * Load and display popular destinations
 */
async function loadDestinations() {
    const destinationsContainer = document.getElementById('destinations-grid');
    
    if (!destinationsContainer) {
        console.error('Destinations container not found');
        return;
    }

    try {
        // Show loading indicator
        showDestinationsLoading(destinationsContainer);

        // Fetch offers to extract destinations
        console.log('Fetching offers to extract destinations...');
        const response = await fetch('http://localhost:8000/api/offers', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('API Response:', data);

        // Extract offers from response
        let offers;
        if (data.status === 'success' && data.data) {
            offers = data.data;
        } else if (Array.isArray(data)) {
            offers = data;
        } else {
            offers = [];
        }

        console.log('Extracted offers:', offers);

        // Extract unique destinations from offers
        const destinations = extractDestinationsFromOffers(offers);
        console.log('Extracted destinations:', destinations);

        // Display the destinations
        displayDestinations(destinations, destinationsContainer);

    } catch (error) {
        console.error('Error loading destinations:', error);
        showDestinationsError(destinationsContainer, error.message);
    }
}

/**
 * Extract unique destinations from offers
 */
function extractDestinationsFromOffers(offers) {
    const destinationMap = new Map();

    offers.forEach(offer => {
        if (offer.destination && offer.destination.trim()) {
            const destination = offer.destination.trim();
            
            if (destinationMap.has(destination)) {
                const existing = destinationMap.get(destination);
                existing.offerCount++;
                // Use the first offer with an image as the representative
                if (!existing.image && offer.images && offer.images.length > 0) {
                    existing.image = offer.images[0].image_path;
                }
                // Update price if this offer has a lower price
                if (offer.prix && (!existing.minPrice || parseFloat(offer.prix) < existing.minPrice)) {
                    existing.minPrice = parseFloat(offer.prix);
                }
            } else {
                destinationMap.set(destination, {
                    name: destination,
                    offerCount: 1,
                    image: offer.images && offer.images.length > 0 ? offer.images[0].image_path : null,
                    minPrice: offer.prix ? parseFloat(offer.prix) : null,
                    sampleOffer: offer
                });
            }
        }
    });

    // Convert to array and sort by offer count (popularity)
    const destinations = Array.from(destinationMap.values())
        .sort((a, b) => b.offerCount - a.offerCount)
        .slice(0, 4); // Take top 4 destinations

    return destinations;
}

/**
 * Display destinations in the grid
 */
function displayDestinations(destinations, container) {
    if (!Array.isArray(destinations) || destinations.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <i class="fas fa-map-marker-alt text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">Aucune destination disponible</h3>
                <p class="text-gray-500 dark:text-gray-500">Revenez plus tard pour découvrir nos destinations.</p>
            </div>
        `;
        return;
    }

    // Clear container
    container.innerHTML = '';

    // Create destination cards
    destinations.forEach(destination => {
        const destinationCard = createDestinationCard(destination);
        container.appendChild(destinationCard);
    });
}

/**
 * Create destination card
 */
function createDestinationCard(destination) {
    const card = document.createElement('div');
    card.className = 'destination-card group relative overflow-hidden rounded-xl shadow-lg transition duration-300 hover:shadow-xl hover:scale-105 cursor-pointer';

    // Get image URL or use a default destination image
    const imageUrl = destination.image 
        ? `http://localhost:8000/storage/${destination.image}`
        : getDefaultDestinationImage(destination.name);

    // Format price
    const priceText = destination.minPrice 
        ? `À partir de €${destination.minPrice}`
        : 'Prix sur demande';

    card.innerHTML = `
        <div class="relative h-64 overflow-hidden">
            <img src="${imageUrl}" 
                 alt="${destination.name}"
                 class="w-full h-full object-cover transition duration-300 group-hover:scale-110"
                 onerror="this.src='${getDefaultDestinationImage(destination.name)}'">
            
            <!-- Overlay gradient -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
            
            <!-- Content overlay -->
            <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                <h3 class="text-xl font-bold mb-2">${destination.name}</h3>
                <p class="text-sm opacity-90 mb-2">${destination.offerCount} offre${destination.offerCount > 1 ? 's' : ''} disponible${destination.offerCount > 1 ? 's' : ''}</p>
                <p class="text-sm font-semibold">${priceText}</p>
            </div>
            
            <!-- Hover overlay -->
            <div class="absolute inset-0 bg-blue-600/20 opacity-0 group-hover:opacity-100 transition duration-300"></div>
        </div>
    `;

    // Add click handler to search for this destination
    card.addEventListener('click', () => {
        searchDestination(destination.name);
    });

    return card;
}

/**
 * Get default image for destination
 */
function getDefaultDestinationImage(destinationName) {
    // Map of destination names to Unsplash images
    const destinationImages = {
        'paris': 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=800&q=80',
        'london': 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=800&q=80',
        'tokyo': 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=800&q=80',
        'new york': 'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?w=800&q=80',
        'dubai': 'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=800&q=80',
        'rome': 'https://images.unsplash.com/photo-1552832230-c0197dd311b5?w=800&q=80',
        'barcelona': 'https://images.unsplash.com/photo-1539037116277-4db20889f2d4?w=800&q=80',
        'istanbul': 'https://images.unsplash.com/photo-1541432901042-2d8bd64b4a9b?w=800&q=80',
        'default': 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800&q=80'
    };

    const name = destinationName.toLowerCase();
    
    // Check if we have a specific image for this destination
    for (const [key, image] of Object.entries(destinationImages)) {
        if (name.includes(key)) {
            return image;
        }
    }
    
    return destinationImages.default;
}

/**
 * Search for destination (scroll to search section and populate)
 */
function searchDestination(destinationName) {
    // Scroll to the search section
    const searchSection = document.getElementById('hotels');
    if (searchSection) {
        searchSection.scrollIntoView({ behavior: 'smooth' });
        
        // Populate the destination field
        setTimeout(() => {
            const cityInput = document.getElementById('city');
            if (cityInput) {
                cityInput.value = destinationName;
                cityInput.focus();
            }
        }, 500);
    }
}

/**
 * Show loading indicator
 */
function showDestinationsLoading(container) {
    container.innerHTML = `
        <div class="col-span-full flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <span class="ml-3 text-gray-600 dark:text-gray-400">Chargement des destinations...</span>
        </div>
    `;
}

/**
 * Show error message
 */
function showDestinationsError(container, message) {
    container.innerHTML = `
        <div class="col-span-full">
            <div class="bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
                <p>Erreur lors du chargement des destinations: ${message}</p>
            </div>
        </div>
    `;
}
