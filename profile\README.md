# User Profile Page - AgencyMo

## Overview
I've created a comprehensive user profile page that allows users to view and edit their personal information, as well as manage their reservations. The implementation follows the existing design patterns and uses localStorage for data management.

## Files Created/Modified

### 1. `profile/profile.js` (NEW)
The main JavaScript file that handles all profile functionality:

**Key Features:**
- **Authentication Check**: Verifies user is logged in and token is valid
- **User Data Management**: Loads user data from localStorage and API
- **Profile Editing**: Modal-based form for updating user information
- **Reservations Display**: Shows user's booking history with details
- **Tab Navigation**: Switches between profile and reservations views
- **Local Storage Integration**: Syncs data between localStorage and API

**Main Functions:**
- `loadUserData()` - Loads and displays user information
- `handleProfileUpdate()` - Processes profile form submissions
- `updateLocalStorageUserData()` - Updates localStorage with new data
- `loadReservations()` - Fetches and displays user reservations
- `showReservationDetails()` - Shows detailed reservation information

### 2. `profile/profile.html` (EXISTING - Enhanced)
The existing HTML structure was already well-designed with:
- Responsive layout using Tailwind CSS
- Dark mode support
- Profile header with user avatar and basic info
- Tabbed interface for profile and reservations
- Modal dialogs for editing and viewing details

### 3. `components/navbar.html` (MODIFIED)
Updated navigation links to point to the profile page:
- Desktop profile link: `/profile/profile.html`
- Mobile profile link: `/profile/profile.html`

### 4. `profile/test-profile.html` (NEW)
A test page that demonstrates the profile functionality with sample data:
- Mock API responses for testing
- Sample user data setup
- Interactive buttons to test different scenarios

## How It Works

### Data Flow
1. **Page Load**: Checks authentication and loads user data from localStorage
2. **API Sync**: Fetches fresh data from API and updates localStorage
3. **Display**: Shows user information in the UI
4. **Edit**: Opens modal with current data for editing
5. **Update**: Sends changes to API and updates localStorage
6. **Refresh**: Reloads display with updated information

### LocalStorage Usage
The profile page uses localStorage to store and manage:
- `auth_token` - Authentication token
- `user_data` - Complete user profile information
- `user_role` - User role (client/admin)
- `token_expires_at` - Token expiration timestamp

### Profile Data Structure
```javascript
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Jean Dupont",
    "email": "<EMAIL>", 
    "phone": "+33 6 12 34 56 78",
    "role": "client",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

## Features

### ✅ Profile Management
- View personal information (name, email, phone, registration date)
- Edit profile through modal form
- Real-time validation
- Success/error messaging
- Automatic localStorage sync

### ✅ Reservations Management
- View all user reservations
- Reservation status indicators (confirmed, pending, cancelled, completed)
- Detailed reservation information modal
- Responsive card layout

### ✅ User Experience
- Smooth tab navigation
- Loading states for API calls
- Error handling with user-friendly messages
- Responsive design for all screen sizes
- Dark mode support
- Consistent styling with the rest of the site

### ✅ Security
- Authentication verification on page load
- Token expiration checking
- Automatic redirect to login if not authenticated
- Secure API communication with Bearer tokens

## API Endpoints Used

The profile page integrates with these API endpoints:
- `GET /api/auth/me` - Fetch current user data
- `PUT /api/auth/profile` - Update user profile
- `GET /api/reservations/user` - Get user reservations
- `GET /api/reservations/{id}` - Get specific reservation details

## Testing

Use the `test-profile.html` file to test the functionality:
1. Open the test page in your browser
2. Click "Setup Test Data" to populate localStorage with sample data
3. Test profile editing, tab navigation, and reservation viewing
4. Use "Clear Test Data" to reset and test authentication flow

## Integration

The profile page is now fully integrated with the existing application:
- Accessible via user menu in the navbar
- Follows the same design patterns as other pages
- Uses the existing authentication system
- Compatible with the dark mode toggle
- Responsive design matches the site's style

## Next Steps

To further enhance the profile functionality, consider:
1. Adding profile picture upload
2. Implementing password change functionality
3. Adding notification preferences
4. Creating a booking history export feature
5. Adding social media profile links
