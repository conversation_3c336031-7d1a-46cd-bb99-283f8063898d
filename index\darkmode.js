    // Dark mode functionality
    const themeToggleBtn = document.getElementById('theme-toggle');
    const themeToggleBtnMobile = document.getElementById('theme-toggle-mobile');
    const themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
    const themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');
    const themeToggleDarkIconMobile = document.getElementById('theme-toggle-dark-icon-mobile');
    const themeToggleLightIconMobile = document.getElementById('theme-toggle-light-icon-mobile');

    // Change the icons inside the button based on previous settings
    if (localStorage.getItem('color-theme') === 'dark' ||
        (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
        themeToggleLightIcon.classList.add('hidden');
        themeToggleDarkIcon.classList.remove('hidden');
        themeToggleLightIconMobile.classList.add('hidden');
        themeToggleDarkIconMobile.classList.remove('hidden');
    } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
        themeToggleDarkIcon.classList.add('hidden');
        themeToggleLightIcon.classList.remove('hidden');
        themeToggleDarkIconMobile.classList.add('hidden');
        themeToggleLightIconMobile.classList.remove('hidden');
    }

    function toggleTheme() {
        // Toggle icons
        themeToggleDarkIcon.classList.toggle('hidden');
        themeToggleLightIcon.classList.toggle('hidden');
        themeToggleDarkIconMobile.classList.toggle('hidden');
        themeToggleLightIconMobile.classList.toggle('hidden');

        // Add rotation animation
        if (themeToggleDarkIcon.classList.contains('hidden')) {
            themeToggleLightIcon.classList.add('rotate');
            themeToggleLightIconMobile.classList.add('rotate');
        } else {
            themeToggleDarkIcon.classList.add('rotate');
            themeToggleDarkIconMobile.classList.add('rotate');
        }

        // Remove rotation animation after it completes
        setTimeout(() => {
            themeToggleLightIcon.classList.remove('rotate');
            themeToggleDarkIcon.classList.remove('rotate');
            themeToggleLightIconMobile.classList.remove('rotate');
            themeToggleDarkIconMobile.classList.remove('rotate');
        }, 500);

        // Toggle dark mode class on document
        if (document.documentElement.classList.contains('dark')) {
            document.documentElement.classList.remove('dark');
            document.body.classList.remove('dark');
            localStorage.setItem('color-theme', 'light');
        } else {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark');
            localStorage.setItem('color-theme', 'dark');
        }
    }

    // Add event listeners to toggle buttons
    themeToggleBtn.addEventListener('click', toggleTheme);
    themeToggleBtnMobile.addEventListener('click', toggleTheme);