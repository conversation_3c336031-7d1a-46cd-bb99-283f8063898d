
        // Global variables
        let map;
        let service;
        let markers = [];
        let mapReady = false;

        // This function will be called when the Google Maps API is loaded
        function initMap() {
            console.log("Map initialization started");

            // Default location (Tunis, Tunisia)
            let initialCenter = { lat: 36.8065, lng: 10.1815 };
            let initialZoom = 10;

            // Get the map element
            const mapElement = document.getElementById('map');
            if (!mapElement) {
                console.error('Map element not found');
                return;
            }

            // Create the map
            map = new google.maps.Map(mapElement, {
                center: initialCenter,
                zoom: initialZoom
            });

            // Initialize Places service
            service = new google.maps.places.PlacesService(map);

            // Set up autocomplete for location input
            setupAutocomplete();

            // Add click event to the map
            map.addListener('click', handleMapClick);

            // Hide loading indicator
            const mapLoading = document.getElementById('map-loading');
            if (mapLoading) {
                mapLoading.style.display = 'none';
            }

            // Clear the timeout
            if (window.googleMapsApiTimeout) {
                clearTimeout(window.googleMapsApiTimeout);
            }

            // Mark map as ready
            mapReady = true;
            console.log("Map is ready");

            // Try to load saved search results
            loadSavedSearchResults();
        }

        // Set up autocomplete for location input
        function setupAutocomplete() {
            const locationInput = document.getElementById('location');
            if (!locationInput) return;

            const autocomplete = new google.maps.places.Autocomplete(locationInput, {
                types: ['(cities)']
            });

            autocomplete.addListener('place_changed', () => {
                const place = autocomplete.getPlace();
                if (!place.geometry) {
                    alert("Please select a valid location.");
                    return;
                }

                // Center the map on the selected location
                map.setCenter(place.geometry.location);
                map.setZoom(12);

                // Search for hotels in the selected area
                searchHotels(place.geometry.location);
            });
        }

        // Handle map click events
        function handleMapClick(event) {
            const location = event.latLng;
            map.setCenter(location);
            map.setZoom(12);
            searchHotels(location);

            // Update the location input with the clicked location
            const locationInput = document.getElementById('location');
            if (locationInput) {
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode({ location: location }, (results, status) => {
                    if (status === 'OK' && results[0]) {
                        locationInput.value = results[0].formatted_address;
                    }
                });
            }
        }

        // Load saved search results from localStorage
        function loadSavedSearchResults() {
            try {
                const savedParams = localStorage.getItem('hotelSearchParams');
                const savedResults = localStorage.getItem('hotelSearchResults');

                if (!savedParams || !savedResults) {
                    console.log("No saved search data found");
                    return;
                }

                const params = JSON.parse(savedParams);
                const results = JSON.parse(savedResults);

                if (!params.lat || !params.lng || !results || results.length === 0) {
                    console.log("Incomplete saved search data");
                    return;
                }

                console.log("Found saved search results:", results.length);

                // Update form fields
                const checkinInput = document.getElementById('checkin');
                const checkoutInput = document.getElementById('checkout');
                const radiusInput = document.getElementById('radius');

                if (checkinInput) checkinInput.value = params.checkin || '';
                if (checkoutInput) checkoutInput.value = params.checkout || '';
                if (radiusInput) radiusInput.value = params.radius / 1000 || 10;

                // Create location object
                const location = new google.maps.LatLng(
                    parseFloat(params.lat),
                    parseFloat(params.lng)
                );

                // Center map
                map.setCenter(location);
                map.setZoom(12);

                // Convert saved results to hotel objects
                const hotels = results.map(hotel => ({
                    name: hotel.name,
                    vicinity: hotel.vicinity,
                    rating: hotel.rating,
                    price: hotel.price,
                    photo: hotel.photo,
                    geometry: {
                        location: new google.maps.LatLng(
                            parseFloat(hotel.lat),
                            parseFloat(hotel.lng)
                        )
                    }
                }));

                // Display the results
                displayHotelResults(hotels, params.checkin, params.checkout);

                // Update location input
                const locationInput = document.getElementById('location');
                if (locationInput) {
                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({ location: location }, (results, status) => {
                        if (status === 'OK' && results[0]) {
                            locationInput.value = results[0].formatted_address;
                        }
                    });
                }
            } catch (error) {
                console.error("Error loading saved search results:", error);
            }
        }



        function searchHotelsFromForm() {
            // Check if map is ready
            if (!mapReady || !map || !service) {
                alert("Map is not ready yet. Please try again in a moment.");
                return;
            }

            // Get location input
            const locationInput = document.getElementById('location');
            if (!locationInput || !locationInput.value) {
                alert("Please enter a location to search.");
                return;
            }

            // Show loading indicator
            const searchLoading = document.getElementById('search-loading');
            if (searchLoading) {
                searchLoading.classList.remove('hidden');
            }

            // Geocode the location
            try {
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode({ address: locationInput.value }, (results, status) => {
                    // Hide loading indicator
                    if (searchLoading) {
                        searchLoading.classList.add('hidden');
                    }

                    if (status === 'OK' && results && results[0] && results[0].geometry) {
                        const location = results[0].geometry.location;
                        searchHotels(location);
                    } else {
                        alert("Unable to find that location. Please try a different address.");
                    }
                });
            } catch (error) {
                console.error("Geocoding error:", error);
                alert("Error searching for location. Please try again.");

                // Hide loading indicator
                if (searchLoading) {
                    searchLoading.classList.add('hidden');
                }
            }
        }

        function clearHotelSearch() {
            console.log("Clearing hotel search");

            try {
                // Clear localStorage
                localStorage.removeItem('hotelSearchParams');
                localStorage.removeItem('hotelSearchResults');

                // Clear form fields
                const locationInput = document.getElementById('location');
                const checkinInput = document.getElementById('checkin');
                const checkoutInput = document.getElementById('checkout');
                const radiusInput = document.getElementById('radius');

                if (locationInput) locationInput.value = '';
                if (checkinInput) checkinInput.value = '';
                if (checkoutInput) checkoutInput.value = '';
                if (radiusInput) radiusInput.value = '10';

                // Clear markers
                clearMarkers();

                // Clear results
                const hotelResults = document.getElementById('hotel-results');
                if (hotelResults) {
                    hotelResults.innerHTML = '';
                }

                // Reset map to default location
                if (map) {
                    map.setCenter({ lat: 36.8065, lng: 10.1815 });
                    map.setZoom(10);
                }

                alert("Search cleared successfully.");
            } catch (error) {
                console.error("Error clearing search:", error);
                alert("Error clearing search. Please try refreshing the page.");
            }
        }

        function searchHotels(location) {
            console.log("Searching hotels at location:", location);

            // Check if map is ready
            if (!mapReady || !map || !service) {
                alert("Map is not ready yet. Please try again in a moment.");
                return;
            }

            // Clear existing markers
            clearMarkers();

            // Get form values
            const radiusInput = document.getElementById('radius');
            const checkinInput = document.getElementById('checkin');
            const checkoutInput = document.getElementById('checkout');
            const hotelResults = document.getElementById('hotel-results');
            const searchLoading = document.getElementById('search-loading');

            if (!radiusInput || !checkinInput || !checkoutInput || !hotelResults || !searchLoading) {
                alert("Required form elements not found. Please refresh the page.");
                return;
            }

            const radius = parseInt(radiusInput.value) * 1000; // Convert km to meters
            const checkin = checkinInput.value;
            const checkout = checkoutInput.value;

            if (!checkin || !checkout) {
                alert("Please select check-in and check-out dates.");
                return;
            }

            // Ensure we have a valid location
            let searchLocation;
            try {
                if (location instanceof google.maps.LatLng) {
                    searchLocation = location;
                } else if (typeof location.lat === 'function' && typeof location.lng === 'function') {
                    searchLocation = location;
                } else if (location.lat && location.lng) {
                    searchLocation = new google.maps.LatLng(
                        parseFloat(location.lat),
                        parseFloat(location.lng)
                    );
                } else {
                    throw new Error("Invalid location format");
                }
            } catch (error) {
                console.error("Location error:", error);
                alert("Invalid location. Please try again.");
                return;
            }

            // Save search parameters
            const searchParams = {
                lat: searchLocation.lat(),
                lng: searchLocation.lng(),
                radius: radius,
                checkin: checkin,
                checkout: checkout
            };
            localStorage.setItem('hotelSearchParams', JSON.stringify(searchParams));

            // Show loading indicator
            searchLoading.classList.remove('hidden');
            hotelResults.innerHTML = '';

            // Create search request
            const request = {
                location: searchLocation,
                radius: radius,
                type: 'lodging',
                keyword: 'hotel'
            };

            // Perform search
            service.nearbySearch(request, (results, status) => {
                // Hide loading indicator
                searchLoading.classList.add('hidden');

                if (status !== google.maps.places.PlacesServiceStatus.OK || !results || results.length === 0) {
                    hotelResults.innerHTML = '<p class="text-yellow-700 font-medium">No hotels found in this area. Try increasing the search radius or selecting a different location.</p>';
                    localStorage.setItem('hotelSearchResults', JSON.stringify([]));
                    return;
                }

                // Filter for hotels
                const hotels = results.filter(place => {
                    if (!place.name) return false;
                    const name = place.name.toLowerCase();
                    return name.includes('hotel') || name.includes('resort') || name.includes('inn') ||
                           name.includes('lodge') || name.includes('ترقي') || name.includes('regency') ||
                           name.includes('suites') || name.includes('palace') || name.includes('residency');
                });

                if (hotels.length === 0) {
                    hotelResults.innerHTML = '<p class="text-yellow-700 font-medium">No hotels found in this area. Try increasing the search radius or selecting a different location.</p>';
                    localStorage.setItem('hotelSearchResults', JSON.stringify([]));
                    return;
                }

                // Save results to localStorage
                const simplifiedHotels = hotels.map(hotel => ({
                    name: hotel.name || 'Hotel',
                    vicinity: hotel.vicinity || 'Unknown location',
                    rating: hotel.rating || 'N/A',
                    lat: hotel.geometry.location.lat(),
                    lng: hotel.geometry.location.lng(),
                    price: (Math.random() * 200 + 50).toFixed(2),
                    photo: hotel.photos && hotel.photos[0] ? hotel.photos[0].getUrl({ maxWidth: 300 }) : 'https://via.placeholder.com/300x200?text=No+Image'
                }));

                localStorage.setItem('hotelSearchResults', JSON.stringify(simplifiedHotels));

                // Display results
                displayHotelResults(hotels, checkin, checkout);
            });
        }

        // Clear all markers from the map
        function clearMarkers() {
            if (markers && markers.length) {
                markers.forEach(marker => marker.setMap(null));
                markers = [];
            }
        }

        // Function to display hotel results
        function displayHotelResults(hotels, checkin, checkout) {
            console.log("Displaying hotel results:", hotels.length);

            // Get the results container
            const hotelResults = document.getElementById('hotel-results');
            if (!hotelResults) {
                console.error("Hotel results container not found");
                return;
            }

            // Create modern header with results count
            let html = `
                <div class="mb-8">
                    <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-3xl font-bold mb-2">
                                    <i class="fas fa-map-marker-alt mr-3"></i>Hôtels près de votre position
                                </h3>
                                <p class="text-blue-100">
                                    <i class="fas fa-hotel mr-2"></i>${hotels.length} hôtel${hotels.length > 1 ? 's' : ''} trouvé${hotels.length > 1 ? 's' : ''}
                                </p>
                            </div>
                            <div class="hidden md:block">
                                <div class="bg-white/20 backdrop-blur-sm rounded-xl p-4">
                                    <i class="fas fa-search-location text-yellow-300 text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            html += '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">';

            // Process each hotel
            hotels.forEach((place, index) => {
                try {
                    // Skip hotels without geometry
                    if (!place.geometry || !place.geometry.location) {
                        return;
                    }

                    // Add marker to the map
                    try {
                        const marker = new google.maps.Marker({
                            position: place.geometry.location,
                            map: map,
                            title: place.name || 'Hotel'
                        });
                        markers.push(marker);
                    } catch (markerError) {
                        console.warn("Error creating marker:", markerError);
                    }

                    // Get hotel details
                    const price = place.price || (Math.random() * 200 + 50).toFixed(2);
                    const rating = place.rating ? place.rating : 'N/A';

                    // Get image URL
                    let image = 'https://via.placeholder.com/300x200?text=No+Image';
                    if (place.photo) {
                        image = place.photo;
                    } else if (place.photos && place.photos[0]) {
                        try {
                            image = place.photos[0].getUrl({ maxWidth: 300 });
                        } catch (e) {
                            // Use default image
                        }
                    }

                    // Get location
                    const vicinity = place.vicinity || 'Location information not available';

                    // Sanitize values
                    const safeName = place.name ? place.name.replace(/[<>"'&]/g, '') : 'Hotel';
                    const safeVicinity = vicinity.replace(/[<>"'&]/g, '');
                    const safeCheckin = checkin ? checkin.replace(/[<>"'&]/g, '') : '';
                    const safeCheckout = checkout ? checkout.replace(/[<>"'&]/g, '') : '';

                    // Generate star rating HTML
                    const generateStarRating = (rating) => {
                        const fullStars = Math.floor(rating);
                        const hasHalfStar = rating % 1 !== 0;
                        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

                        let starsHtml = '';

                        // Full stars
                        for (let i = 0; i < fullStars; i++) {
                            starsHtml += '<i class="fas fa-star text-yellow-400"></i>';
                        }

                        // Half star
                        if (hasHalfStar) {
                            starsHtml += '<i class="fas fa-star-half-alt text-yellow-400"></i>';
                        }

                        // Empty stars
                        for (let i = 0; i < emptyStars; i++) {
                            starsHtml += '<i class="far fa-star text-gray-300"></i>';
                        }

                        return starsHtml;
                    };

                    // Create modern hotel card
                    html += `
                        <div id="hotel-${index}" class="hotel-card group bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl overflow-hidden transition-all duration-300 hover:scale-105 border border-gray-100 dark:border-gray-700">
                            <!-- Image Section -->
                            <div class="relative overflow-hidden">
                                <img src="${image}" alt="${safeName}" class="w-full h-56 object-cover transition-transform duration-500 group-hover:scale-110">

                                <!-- Rating Badge -->
                                <div class="absolute top-4 left-4 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm px-3 py-1 rounded-full shadow-lg">
                                    <div class="flex items-center space-x-1">
                                        ${generateStarRating(rating)}
                                        <span class="text-sm font-semibold text-gray-800 dark:text-white ml-1">${rating}</span>
                                    </div>
                                </div>

                                <!-- Price Badge -->
                                <div class="absolute top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-full shadow-lg">
                                    <div class="text-center">
                                        <div class="text-lg font-bold">$${price}</div>
                                        <div class="text-xs opacity-90">par nuit</div>
                                    </div>
                                </div>

                                <!-- Gradient Overlay -->
                                <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>

                            <!-- Content Section -->
                            <div class="p-6">
                                <!-- Hotel Info -->
                                <div class="mb-4">
                                    <h4 class="text-xl font-bold text-gray-900 dark:text-white mb-2 line-clamp-1">${safeName}</h4>
                                    <div class="flex items-center text-gray-600 dark:text-gray-400 mb-3">
                                        <i class="fas fa-map-marker-alt text-blue-600 dark:text-blue-400 mr-2"></i>
                                        <span class="text-sm line-clamp-1">${safeVicinity}</span>
                                    </div>
                                </div>

                                <!-- Booking Form -->
                                <form method="POST" action="" class="space-y-4">
                                    <input type="hidden" name="hotel_name" value="${safeName}">
                                    <input type="hidden" name="hotel_location" value="${safeVicinity}">
                                    <input type="hidden" name="checkin" value="${safeCheckin}">
                                    <input type="hidden" name="checkout" value="${safeCheckout}">
                                    <input type="hidden" name="adults" value="1">
                                    <input type="hidden" name="children" value="0">
                                    <input type="hidden" name="infants" value="0">

                                    <!-- Hidden default values for required fields -->
                                    <input type="hidden" name="rooms" value="1">
                                    <input type="hidden" name="room_type" value="standard">
                                    <input type="hidden" name="meal_plan" value="no_meals">
                                    <input type="hidden" name="special_requests" value="">

                                    <!-- Action Button -->
                                    <button type="button" onclick="openHotelWebsite('${safeName}', '${safeVicinity}')"
                                            class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800
                                                   text-white py-3 px-4 rounded-xl font-semibold text-sm
                                                   transition-all duration-300 transform hover:scale-105 hover:shadow-lg
                                                   flex items-center justify-center space-x-2">
                                        <i class="fas fa-external-link-alt"></i>
                                        <span>Reserver</span>
                                    </button>
                                </form>
                            </div>
                        </div>
                    `;
                } catch (error) {
                    console.error("Error displaying hotel:", error);
                }
            });

            // Close the grid and update the DOM
            html += '</div>';
            hotelResults.innerHTML = html;
        }

        // Function to open hotel website
        function openHotelWebsite(hotelName, hotelLocation) {
            // Try to get hotel details from Google Places API to find website
            if (service) {
                const request = {
                    query: `${hotelName} ${hotelLocation}`,
                    fields: ['website', 'url', 'place_id']
                };

                service.textSearch(request, (results, status) => {
                    if (status === google.maps.places.PlacesServiceStatus.OK && results[0]) {
                        const place = results[0];

                        // Get detailed place information including website
                        service.getDetails({
                            placeId: place.place_id,
                            fields: ['website', 'url']
                        }, (placeDetails, detailsStatus) => {
                            if (detailsStatus === google.maps.places.PlacesServiceStatus.OK && placeDetails) {
                                if (placeDetails.website) {
                                    // Open hotel's official website
                                    window.open(placeDetails.website, '_blank');
                                    return;
                                }
                            }

                            // Fallback: Search on Google
                            const searchQuery = encodeURIComponent(`${hotelName} ${hotelLocation} official website`);
                            window.open(`https://www.google.com/search?q=${searchQuery}`, '_blank');
                        });
                    } else {
                        // Fallback: Search on Google
                        const searchQuery = encodeURIComponent(`${hotelName} ${hotelLocation} official website`);
                        window.open(`https://www.google.com/search?q=${searchQuery}`, '_blank');
                    }
                });
            } else {
                // Fallback: Search on Google
                const searchQuery = encodeURIComponent(`${hotelName} ${hotelLocation} official website`);
                window.open(`https://www.google.com/search?q=${searchQuery}`, '_blank');
            }
        }

        // Make function globally available
        window.openHotelWebsite = openHotelWebsite;

        document.addEventListener('DOMContentLoaded', () => {
            const checkinInput = document.getElementById('checkin');
            const checkoutInput = document.getElementById('checkout');
            const radiusInput = document.getElementById('radius');
            const locationInput = document.getElementById('location');

            // We now handle restoring search results directly in the initMap function
            // This ensures proper timing with the Google Maps API initialization

            // Set up event listeners
            if (checkinInput && checkoutInput) {
                checkinInput.addEventListener('change', () => {
                    const checkinDate = new Date(checkinInput.value);
                    const minCheckoutDate = new Date(checkinDate);
                    minCheckoutDate.setDate(checkinDate.getDate() + 1);
                    checkoutInput.min = minCheckoutDate.toISOString().split('T')[0];
                    if (new Date(checkoutInput.value) <= checkinDate) {
                        checkoutInput.value = '';
                    }
                });

                checkoutInput.addEventListener('change', () => {
                    const checkinDate = new Date(checkinInput.value);
                    const checkoutDate = new Date(checkoutInput.value);
                    if (checkoutDate <= checkinDate) {
                        alert('Check-out date must be after the check-in date.');
                        checkoutInput.value = '';
                    }
                });
            }

            if (radiusInput) {
                radiusInput.addEventListener('change', () => {
                    if (locationInput && locationInput.value) {
                        const geocoder = new google.maps.Geocoder();
                        geocoder.geocode({ address: locationInput.value }, (results, status) => {
                            if (status === 'OK' && results[0]) {
                                const location = results[0].geometry.location;
                                searchHotels(location);
                            }
                        });
                    }
                });
            }

            const departDateInput = document.getElementById('depart_date');
            if (departDateInput) {
                departDateInput.addEventListener('change', () => {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    const departDate = new Date(departDateInput.value);
                    if (departDate < today) {
                        alert('Departure date cannot be in the past.');
                        departDateInput.value = '';
                    }
                });
            }

            // We now handle restoring search results directly in the initMap function
            // This ensures proper timing with the Google Maps API initialization

            window.removeBooking = (index) => {
                if (confirm('Are you sure you want to remove this booking?')) {
                    // Remove the booking from the array
                    fetch('remove_booking.php?index=' + index)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('Error removing booking');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Error removing booking');
                        });
                }
            };
        });
