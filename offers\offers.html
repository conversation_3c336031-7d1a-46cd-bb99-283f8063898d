<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#ffffff">
    <title>Travel Agency - Exclusive Offers</title>
    <link rel="stylesheet" href="offers.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="offers/tailind.js"></script>

    <!-- Load Navbar Script -->
    <script src="/components/load-navbar.js"></script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white font-sans">
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <!-- Hero Section -->
    <div class="relative h-96 bg-blue-800 overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="parallax-bg absolute inset-0" style="background-image: url('https://images.unsplash.com/photo-1506929562872-bb421503ef21?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');"></div>
        <div class="relative z-10 flex flex-col items-center justify-center h-full text-center px-4">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">Exclusive Travel Offers</h1>
            <p class="text-xl text-white mb-8 max-w-2xl">Discover our limited-time deals to the world's most amazing destinations</p>
            <a href="#offers" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-full transition duration-300 transform hover:scale-105">
                Explore Offers
            </a>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="container mx-auto px-6 py-8 -mt-16 z-20 relative">
        <div class="search-box bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg dark:shadow-gray-900">
            <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4">
                <div class="w-full md:w-1/3">
                    <label class="block text-gray-700 dark:text-gray-300 font-medium mb-2">Destination</label>
                    <div class="relative">
                        <i class="fas fa-map-marker-alt absolute left-3 top-3 text-gray-400"></i>
                        <input type="text" id="destination-input" placeholder="Where to?" class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="w-full md:w-1/4">
                    <label class="block text-gray-700 dark:text-gray-300 font-medium mb-2">Travel Dates</label>
                    <div class="relative">
                        <i class="fas fa-calendar-alt absolute left-3 top-3 text-gray-400"></i>
                        <input type="date" id="date-input" class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="w-full md:w-1/4">
                    <label class="block text-gray-700 dark:text-gray-300 font-medium mb-2">Price Range</label>
                    <select id="price-range" class="w-full pl-4 pr-10 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="any">Any Price</option>
                        <option value="under-500">Under €500</option>
                        <option value="500-1000">€500 - €1000</option>
                        <option value="1000-2000">€1000 - €2000</option>
                        <option value="over-2000">Over €2000</option>
                    </select>
                </div>
                <div class="w-full md:w-auto self-end">
                    <button id="search-btn" type="button" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-search mr-2"></i> Search
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Offers Section -->
    <section id="offers" class="container mx-auto px-6 py-12">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-3xl font-bold text-gray-800 dark:text-white">Current Offers</h2>
            <div class="flex space-x-2">
                <button type="button" class="filter-btn px-4 py-2 bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200 rounded-lg font-medium">All</button>
                <button type="button" class="filter-btn px-4 py-2 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-700 dark:hover:text-blue-200 dark:text-gray-300 rounded-lg font-medium">Beach</button>
                <button type="button" class="filter-btn px-4 py-2 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-700 dark:hover:text-blue-200 dark:text-gray-300 rounded-lg font-medium">Mountain</button>
                <button type="button" class="filter-btn px-4 py-2 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-700 dark:hover:text-blue-200 dark:text-gray-300 rounded-lg font-medium">City</button>
                <button type="button" class="filter-btn px-4 py-2 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-700 dark:hover:text-blue-200 dark:text-gray-300 rounded-lg font-medium">Adventure</button>
            </div>
        </div>

        <!-- Loading indicator -->
        <div id="loading" class="hidden flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>

        <!-- Error message -->
        <div id="error" class="hidden bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline" id="error-message"> Failed to load offers.</span>
        </div>

        <!-- Offers container -->
        <div id="offers-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Offers will be dynamically loaded here -->
        </div>

        <div class="mt-12 text-center">
            <button id="load-more-btn" type="button" class="hidden bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-full transition duration-300 transform hover:scale-105">
                Load More Offers
            </button>
        </div>
    </section>

    <!-- Special Deals Banner -->
    <div class="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-8">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="mb-4 md:mb-0">
                    <h3 class="text-2xl font-bold">Last Minute Deals!</h3>
                    <p class="text-blue-200">Book now and save up to 50% on selected destinations</p>
                </div>
                <button type="button" class="bg-white text-blue-700 hover:bg-blue-100 font-bold py-2 px-6 rounded-full transition duration-300">
                     <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Testimonials -->


    <!-- Newsletter -->


    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <i class="fas fa-plane-departure text-2xl mr-2 text-blue-400"></i>
                        <span class="font-semibold text-xl">Global Travels</span>
                    </div>
                    <p class="text-gray-400 mb-4">Making your travel dreams a reality since 2005.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Home</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Destinations</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Special Offers</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-4">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">FAQs</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Booking Guide</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Cancellation Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Terms of Service</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-4">Contact Us</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-blue-400"></i>
                            <span>123 Travel Street, Suite 100, New York, NY 10001</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 text-blue-400"></i>
                            <span>+****************</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-blue-400"></i>
                            <span><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
                <p>&copy; 2023 Global Travels. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Image Lightbox Modal -->
    <div id="image-lightbox" class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 hidden">
        <div class="relative max-w-7xl max-h-full w-full h-full flex items-center justify-center p-4">
            <!-- Close button -->
            <button type="button" id="lightbox-close" class="absolute top-4 right-4 text-white hover:text-gray-300 text-2xl z-10">
                <i class="fas fa-times"></i>
            </button>

            <!-- Navigation buttons -->
            <button type="button" id="lightbox-prev" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 text-3xl z-10 hidden">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button type="button" id="lightbox-next" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 text-3xl z-10 hidden">
                <i class="fas fa-chevron-right"></i>
            </button>

            <!-- Image container -->
            <div class="relative max-w-full max-h-full">
                <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain">

                <!-- Loading spinner -->
                <div id="lightbox-loading" class="absolute inset-0 flex items-center justify-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
                </div>

                <!-- Image info -->
                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4">
                    <p id="lightbox-caption" class="text-center"></p>
                    <p id="lightbox-counter" class="text-center text-sm opacity-75 mt-1"></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Offer Detail Modal -->
    <div id="offer-detail-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4 shadow-xl">
            <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white" id="detail-offer-title">Offer Details</h3>
                <button type="button" id="close-detail-modal" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 text-xl">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="p-6">
                <!-- Image Gallery Section -->
                <div class="mb-6">
                    <!-- Main Image -->
                    <div class="relative mb-4">
                        <div id="detail-main-image-container" class="relative h-64 md:h-80 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                            <img id="detail-main-image" src="" alt="" class="w-full h-full object-cover cursor-pointer">

                            <!-- Image loading skeleton -->
                            <div id="detail-image-skeleton" class="absolute inset-0 bg-gray-300 dark:bg-gray-600 animate-pulse flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-4xl"></i>
                            </div>

                            <!-- Image count badge -->
                            <div id="detail-image-count" class="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm hidden">
                                <i class="fas fa-images mr-1"></i>
                                <span id="detail-image-count-text">0 photos</span>
                            </div>
                        </div>

                        <!-- Thumbnail strip -->
                        <div id="detail-thumbnails-container" class="hidden">
                            <div class="flex space-x-2 mt-4 overflow-x-auto pb-2">
                                <div id="detail-thumbnails" class="flex space-x-2">
                                    <!-- Thumbnails will be inserted here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Offer Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Description</h4>
                        <p id="detail-description" class="text-gray-600 dark:text-gray-300 mb-4">Loading...</p>

                        <div class="space-y-2">
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-map-marker-alt mr-3 text-blue-500"></i>
                                <span id="detail-destination">Loading...</span>
                            </div>
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-calendar-alt mr-3 text-blue-500"></i>
                                <span id="detail-dates">Loading...</span>
                            </div>
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-euro-sign mr-3 text-blue-500"></i>
                                <span id="detail-price" class="font-semibold">Loading...</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Additional Information</h4>
                        <div id="detail-additional-info" class="space-y-2 text-gray-600 dark:text-gray-300">
                            <!-- Additional info will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Action buttons -->
                <div class="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <button type="button" id="close-detail-btn" class="px-6 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                        Close
                    </button>
                    <button type="button" id="reserve-from-detail" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                        Make Reservation
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservation Modal -->
    <div id="reservation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6 shadow-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Confirm Reservation</h3>
                <button type="button" id="close-modal" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Loading state -->
            <div id="modal-loading" class="hidden flex justify-center items-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>

            <!-- Error message -->
            <div id="modal-error" class="hidden bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded relative mb-4" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline" id="modal-error-message">Failed to make reservation.</span>
            </div>

            <!-- Success message -->
            <div id="modal-success" class="hidden bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-700 text-green-700 dark:text-green-300 px-4 py-3 rounded relative mb-4" role="alert">
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline" id="modal-success-message">Reservation successful!</span>
            </div>

            <!-- Reservation details -->
            <div id="reservation-details" class="mb-6">
                <p class="text-gray-700 dark:text-gray-300 mb-4">You are about to make a reservation for:</p>
                <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4">
                    <h4 id="offer-title" class="font-bold text-gray-800 dark:text-white mb-2">Offer Title</h4>
                    <div class="flex items-center text-gray-600 dark:text-gray-400 mb-2">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        <span id="offer-destination">Destination</span>
                    </div>
                    <div class="flex items-center text-gray-600 dark:text-gray-400">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <span id="offer-dates">Dates</span>
                    </div>
                </div>

                <!-- Visa warning (hidden by default) -->
                <div id="visa-warning" class="hidden mb-4"></div>

                <p class="text-gray-700 dark:text-gray-300">Do you want to proceed with this reservation?</p>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" id="cancel-reservation" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                    Cancel
                </button>
                <button type="button" id="confirm-reservation" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                    Confirm Reservation
                </button>
            </div>
        </div>
    </div>

    <!-- Include the OffersAPI class first -->
    <script src="/offers.js"></script>
    <!-- Include the unified offers API script -->
    <script src="offers-api.js"></script>
    <script src="/index/darkmode.js"></script>

</body>
</html>