// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in and is an admin
    checkAdminAuth();

    // Initialize page
    initPage();

    // Add event listeners
    addEventListeners();
});

// Global variables
let allReservations = [];
let filteredReservations = [];
let allUsers = [];
let allOffers = [];
let currentPage = 1;
let itemsPerPage = 10;
let selectedReservationId = null;
let isEditMode = false;

// Function to check if user is logged in and is an admin
function checkAdminAuth() {
    const token = localStorage.getItem('auth_token');
    const userRole = localStorage.getItem('user_role');

    if (!token) {
        // User is not logged in, redirect to login page
        window.location.href = '../login.html';
        return;
    }

    if (userRole !== 'admin') {
        // User is not an admin, redirect to home page
        alert('Access denied: You do not have admin privileges.');
        window.location.href = '/index/index.html';
        return;
    }

    // User is logged in and is an admin, fetch user data
    fetchUserData();
}

// Function to fetch user data
function fetchUserData() {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (userData) {
        try {
            const user = JSON.parse(userData);
            const name = user.name || (user.data && user.data.name) || 'Admin';

            // Update admin name in sidebar
            const adminNameElement = document.getElementById('admin-name');
            if (adminNameElement) {
                adminNameElement.textContent = name;
            }
        } catch (error) {
            console.error('Error parsing user data:', error);
        }
    }

    // Refresh user data from API
    fetch('http://localhost:8000/api/auth/me', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch user data: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('User data fetched:', data);

        // Store updated user data
        localStorage.setItem('user_data', JSON.stringify(data));

        // Update admin name in sidebar
        const name = data.name || (data.data && data.data.name) || 'Admin';
        const adminNameElement = document.getElementById('admin-name');
        if (adminNameElement) {
            adminNameElement.textContent = name;
        }
    })
    .catch(error => {
        console.error('Error fetching user data:', error);
    });
}

// Function to initialize page
function initPage() {
    // Fetch all necessary data
    fetchReservations();
    fetchUsers();
    fetchOffers();

    // Initialize weather widget
    initWeatherWidget();
}

// Function to initialize weather widget
function initWeatherWidget() {
    // Show weather widget
    const weatherWidget = document.getElementById('weather-widget');
    if (weatherWidget) {
        weatherWidget.classList.remove('hidden');
    }
}

// Function to fetch reservations
function fetchReservations() {
    const token = localStorage.getItem('auth_token');

    // Show loading state
    const reservationsTable = document.getElementById('reservations-table');
    if (reservationsTable) {
        reservationsTable.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    <div class="flex justify-center items-center">
                        <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500 mr-3"></div>
                        Loading reservations...
                    </div>
                </td>
            </tr>
        `;
    }

    // Fetch reservations from API
    fetch('http://localhost:8000/api/reservation-offers', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch reservations: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Reservations fetched:', data);

        // Extract reservations data
        if (Array.isArray(data)) {
            allReservations = data;
        } else if (data.data && Array.isArray(data.data)) {
            allReservations = data.data;
        } else if (data.reservations && Array.isArray(data.reservations)) {
            allReservations = data.reservations;
        } else {
            allReservations = [];
            console.warn('Unexpected API response format:', data);
        }

        // Apply filters to update the table
        applyFilters();
    })
    .catch(error => {
        console.error('Error fetching reservations:', error);

        // Show error message in table
        if (reservationsTable) {
            reservationsTable.innerHTML = `
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-red-500 dark:text-red-400">
                        Error loading reservations: ${error.message}
                    </td>
                </tr>
            `;
        }

        // Set empty reservations array
        allReservations = [];
    });
}

// Function to fetch users
function fetchUsers() {
    const token = localStorage.getItem('auth_token');

    // Fetch users from API
    fetch('http://localhost:8000/api/users', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch users: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Users fetched:', data);

        // Extract users data
        if (Array.isArray(data)) {
            allUsers = data;
        } else if (data.data && Array.isArray(data.data)) {
            allUsers = data.data;
        } else if (data.users && Array.isArray(data.users)) {
            allUsers = data.users;
        } else {
            allUsers = [];
            console.warn('Unexpected API response format:', data);
        }

        // Populate user dropdown
        populateUserDropdown();
    })
    .catch(error => {
        console.error('Error fetching users:', error);
        allUsers = [];
    });
}

// Function to fetch offers
function fetchOffers() {
    const token = localStorage.getItem('auth_token');

    // Fetch offers from API
    fetch('http://localhost:8000/api/offers', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch offers: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Offers fetched:', data);

        // Extract offers data
        if (Array.isArray(data)) {
            allOffers = data;
        } else if (data.data && Array.isArray(data.data)) {
            allOffers = data.data;
        } else if (data.offers && Array.isArray(data.offers)) {
            allOffers = data.offers;
        } else {
            allOffers = [];
            console.warn('Unexpected API response format:', data);
        }

        // Populate offer dropdown
        populateOfferDropdown();
    })
    .catch(error => {
        console.error('Error fetching offers:', error);
        allOffers = [];
    });
}

// Function to populate user dropdown
function populateUserDropdown() {
    const userSelect = document.getElementById('user-id');

    if (!userSelect) return;

    // Clear existing options except the first one
    while (userSelect.options.length > 1) {
        userSelect.remove(1);
    }

    // Add user options
    allUsers.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = `${user.name} (${user.email})`;
        userSelect.appendChild(option);
    });
}

// Function to populate offer dropdown
function populateOfferDropdown() {
    const offerSelect = document.getElementById('offer-id');

    if (!offerSelect) return;

    // Clear existing options except the first one
    while (offerSelect.options.length > 1) {
        offerSelect.remove(1);
    }

    // Add offer options
    allOffers.forEach(offer => {
        const option = document.createElement('option');
        option.value = offer.id;
        option.textContent = `${offer.titre} - ${offer.destination} (${parseFloat(offer.prix).toLocaleString('fr-FR', {style: 'currency', currency: 'EUR'})})`;

        // Add destination as data attribute for weather API
        if (offer.destination) {
            option.setAttribute('data-destination', offer.destination);
        }

        offerSelect.appendChild(option);
    });
}

// Function to apply filters
function applyFilters() {
    const searchTerm = document.getElementById('search').value.toLowerCase();
    const statusFilter = document.getElementById('status-filter').value;

    // Filter reservations based on search term and status
    filteredReservations = allReservations.filter(reservation => {
        // Find related user and offer
        const user = allUsers.find(u => u.id == reservation.user_id);
        const offer = allOffers.find(o => o.id == reservation.offer_id);

        // Search term filter
        const matchesSearch =
            (user && user.name && user.name.toLowerCase().includes(searchTerm)) ||
            (user && user.email && user.email.toLowerCase().includes(searchTerm)) ||
            (offer && offer.titre && offer.titre.toLowerCase().includes(searchTerm)) ||
            (offer && offer.destination && offer.destination.toLowerCase().includes(searchTerm)) ||
            (reservation.id && reservation.id.toString().includes(searchTerm));

        // Status filter
        let matchesStatus = true;
        if (statusFilter !== 'all') {
            matchesStatus = reservation.status === statusFilter;
        }

        return matchesSearch && matchesStatus;
    });

    // Reset to first page
    currentPage = 1;

    // Update table and pagination
    updateTable();
    updatePagination();
}

// Function to update table
function updateTable() {
    const reservationsTable = document.getElementById('reservations-table');

    if (!reservationsTable) return;

    if (filteredReservations.length === 0) {
        reservationsTable.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    No reservations found.
                </td>
            </tr>
        `;
        return;
    }

    // Calculate pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredReservations.length);
    const paginatedReservations = filteredReservations.slice(startIndex, endIndex);

    // Update pagination info
    document.getElementById('page-start').textContent = startIndex + 1;
    document.getElementById('page-end').textContent = endIndex;
    document.getElementById('total-items').textContent = filteredReservations.length;

    // Clear table
    reservationsTable.innerHTML = '';

    // Add reservations to table
    paginatedReservations.forEach(reservation => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150';

        // Find related user and offer
        const user = allUsers.find(u => u.id == reservation.user_id) || { name: 'Unknown', email: 'Unknown' };
        const offer = allOffers.find(o => o.id == reservation.offer_id) || { titre: 'Unknown', destination: 'Unknown' };

        // Format date
        const reservationDate = new Date(reservation.reservation_date);
        const formattedDate = reservationDate.toLocaleDateString();

        // Create status badge
        let statusBadge;
        switch (reservation.status) {
            case 'confirmed':
                statusBadge = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300">Confirmed</span>';
                break;
            case 'cancelled':
                statusBadge = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300">Cancelled</span>';
                break;
            default:
                statusBadge = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300">Pending</span>';
        }

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900 dark:text-white">${reservation.id}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900 dark:text-white">${user.name}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">${user.email}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900 dark:text-white">${offer.titre}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">${offer.destination}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${formattedDate}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                ${statusBadge}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <a href="#" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3 edit-reservation" data-reservation-id="${reservation.id}">Edit</a>
                <a href="#" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 mr-3 delete-reservation" data-reservation-id="${reservation.id}">Delete</a>
                ${offer.destination ? `<a href="#" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 check-weather" data-destination="${offer.destination}"><i class="fas fa-cloud-sun" title="Check Weather"></i></a>` : ''}
            </td>
        `;

        reservationsTable.appendChild(row);
    });

    // Add event listeners to action buttons
    document.querySelectorAll('.edit-reservation').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const reservationId = this.getAttribute('data-reservation-id');
            editReservation(reservationId);
        });
    });

    document.querySelectorAll('.delete-reservation').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const reservationId = this.getAttribute('data-reservation-id');
            if (confirm('Are you sure you want to delete this reservation?')) {
                deleteReservation(reservationId);
            }
        });
    });

    // Add event listeners to weather check buttons
    document.querySelectorAll('.check-weather').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const destination = this.getAttribute('data-destination');
            if (destination && typeof window.getWeatherForDestination === 'function') {
                window.getWeatherForDestination(destination);

                // Scroll to weather widget
                const weatherWidget = document.getElementById('weather-widget');
                if (weatherWidget) {
                    weatherWidget.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
    });
}

// Function to update pagination
function updatePagination() {
    const paginationContainer = document.getElementById('pagination');
    const totalPages = Math.ceil(filteredReservations.length / itemsPerPage);

    // Update mobile pagination buttons
    const mobilePrevPage = document.getElementById('mobile-prev-page');
    const mobileNextPage = document.getElementById('mobile-next-page');

    mobilePrevPage.disabled = currentPage === 1;
    mobileNextPage.disabled = currentPage === totalPages;

    // Update desktop pagination buttons
    const prevPage = document.getElementById('prev-page');
    const nextPage = document.getElementById('next-page');

    prevPage.disabled = currentPage === 1;
    nextPage.disabled = currentPage === totalPages;

    // Remove existing page buttons
    const pageButtons = paginationContainer.querySelectorAll('.page-button');
    pageButtons.forEach(button => button.remove());

    // Add page buttons
    for (let i = 1; i <= totalPages; i++) {
        const pageButton = document.createElement('button');
        pageButton.className = `page-button relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium ${
            i === currentPage
                ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30'
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
        }`;
        pageButton.textContent = i;
        pageButton.addEventListener('click', () => {
            currentPage = i;
            updateTable();
            updatePagination();
        });

        // Insert before the next button
        paginationContainer.insertBefore(pageButton, nextPage);
    }
}

// Function to show reservation modal for adding
function showAddReservationModal() {
    const modal = document.getElementById('reservation-modal');
    const modalTitle = document.getElementById('modal-title');
    const form = document.getElementById('reservation-form');
    const submitBtn = document.getElementById('submit-btn');

    // Reset form
    form.reset();
    document.getElementById('reservation-id').value = '';

    // Set today's date as default
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    document.getElementById('reservation-date').value = formattedDate;

    // Update modal title and button text
    modalTitle.textContent = 'Add New Reservation';
    submitBtn.textContent = 'Submit Reservation';

    // Hide messages
    hideModalMessages();

    // Set mode
    isEditMode = false;

    // Show modal
    modal.classList.remove('hidden');
}

// Function to show reservation modal for editing
function editReservation(reservationId) {
    const reservation = allReservations.find(r => r.id == reservationId);

    if (!reservation) {
        alert('Reservation not found.');
        return;
    }

    const modal = document.getElementById('reservation-modal');
    const modalTitle = document.getElementById('modal-title');
    const form = document.getElementById('reservation-form');
    const submitBtn = document.getElementById('submit-btn');

    // Reset form
    form.reset();

    // Set reservation data
    document.getElementById('reservation-id').value = reservation.id;
    document.getElementById('user-id').value = reservation.user_id;
    document.getElementById('offer-id').value = reservation.offer_id;

    // Format date for input
    const reservationDate = new Date(reservation.reservation_date);
    const formattedDate = reservationDate.toISOString().split('T')[0];
    document.getElementById('reservation-date').value = formattedDate;

    document.getElementById('status').value = reservation.status || 'pending';
    document.getElementById('notes').value = reservation.notes || '';

    // Update modal title and button text
    modalTitle.textContent = 'Edit Reservation';
    submitBtn.textContent = 'Update Reservation';

    // Hide messages
    hideModalMessages();

    // Set mode
    isEditMode = true;
    selectedReservationId = reservationId;

    // Show modal
    modal.classList.remove('hidden');

    // Update weather widget for the destination
    const offer = allOffers.find(o => o.id == reservation.offer_id);
    if (offer && offer.destination && typeof window.getWeatherForDestination === 'function') {
        window.getWeatherForDestination(offer.destination);
    }
}

// Function to hide reservation modal
function hideReservationModal() {
    const modal = document.getElementById('reservation-modal');
    modal.classList.add('hidden');
}

// Function to show modal error message
function showModalError(message) {
    const errorContainer = document.getElementById('modal-error');
    const errorMessage = document.getElementById('error-message');
    const successContainer = document.getElementById('modal-success');

    if (errorMessage) errorMessage.textContent = message;
    if (errorContainer) errorContainer.classList.remove('hidden');
    if (successContainer) successContainer.classList.add('hidden');
}

// Function to show modal success message
function showModalSuccess(message) {
    const successContainer = document.getElementById('modal-success');
    const successMessage = document.getElementById('success-message');
    const errorContainer = document.getElementById('modal-error');

    if (successMessage) successMessage.textContent = message;
    if (successContainer) successContainer.classList.remove('hidden');
    if (errorContainer) errorContainer.classList.add('hidden');
}

// Function to hide modal messages
function hideModalMessages() {
    const errorContainer = document.getElementById('modal-error');
    const successContainer = document.getElementById('modal-success');

    if (errorContainer) errorContainer.classList.add('hidden');
    if (successContainer) successContainer.classList.add('hidden');
}

// Function to submit reservation form
function submitReservationForm(event) {
    event.preventDefault();

    // Get form data
    const userId = document.getElementById('user-id').value;
    const offerId = document.getElementById('offer-id').value;
    const reservationDate = document.getElementById('reservation-date').value;
    const status = document.getElementById('status').value;
    const notes = document.getElementById('notes').value;
    const reservationId = document.getElementById('reservation-id').value;

    // Validate form
    if (!userId || !offerId || !reservationDate || !status) {
        showModalError('Please fill in all required fields.');
        return;
    }

    // Prepare data
    const reservationData = {
        user_id: userId,
        offer_id: offerId,
        reservation_date: reservationDate,
        status: status,
        notes: notes
    };

    // Get token
    const token = localStorage.getItem('auth_token');

    // Disable submit button
    const submitBtn = document.getElementById('submit-btn');
    const originalButtonText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';

    // Determine if this is an add or update operation
    const method = isEditMode ? 'PUT' : 'POST';
    const url = isEditMode
        ? `http://localhost:8000/api/reservation-offers/${reservationId}`
        : 'http://localhost:8000/api/reservation-offers';

    // Submit data to API
    fetch(url, {
        method: method,
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify(reservationData)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.message || `Error ${response.status}: ${response.statusText}`);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('Reservation submitted:', data);

        // Show success message
        const message = isEditMode ? 'Reservation updated successfully!' : 'Reservation added successfully!';
        showModalSuccess(message);

        // Refresh reservations
        fetchReservations();

        // Close modal after a delay
        setTimeout(() => {
            hideReservationModal();
        }, 2000);
    })
    .catch(error => {
        console.error('Error submitting reservation:', error);
        showModalError(error.message || 'Failed to submit reservation. Please try again.');
    })
    .finally(() => {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.textContent = originalButtonText;
    });
}

// Function to delete a reservation
function deleteReservation(reservationId) {
    const token = localStorage.getItem('auth_token');

    fetch(`http://localhost:8000/api/reservation-offers/${reservationId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.message || `Error ${response.status}: ${response.statusText}`);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('Reservation deleted:', data);

        // Show success message
        alert('Reservation deleted successfully!');

        // Refresh reservations
        fetchReservations();
    })
    .catch(error => {
        console.error('Error deleting reservation:', error);
        alert(`Error deleting reservation: ${error.message}`);
    });
}

// Function to add event listeners
function addEventListeners() {
    // Add reservation button
    const addReservationBtn = document.getElementById('add-reservation-btn');
    if (addReservationBtn) {
        addReservationBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddReservationModal();
        });
    }

    // Modal close button
    const closeModalBtn = document.getElementById('close-modal');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', hideReservationModal);
    }

    // Cancel button
    const cancelBtn = document.getElementById('cancel-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', hideReservationModal);
    }

    // Reservation form
    const reservationForm = document.getElementById('reservation-form');
    if (reservationForm) {
        reservationForm.addEventListener('submit', submitReservationForm);
    }

    // Search button
    const searchBtn = document.getElementById('search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', applyFilters);
    }

    // Search input (search on Enter key)
    const searchInput = document.getElementById('search');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    }

    // Status filter
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }

    // Pagination buttons
    const prevPage = document.getElementById('prev-page');
    const nextPage = document.getElementById('next-page');
    const mobilePrevPage = document.getElementById('mobile-prev-page');
    const mobileNextPage = document.getElementById('mobile-next-page');

    if (prevPage) {
        prevPage.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                updateTable();
                updatePagination();
            }
        });
    }

    if (nextPage) {
        nextPage.addEventListener('click', function() {
            const totalPages = Math.ceil(filteredReservations.length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                updateTable();
                updatePagination();
            }
        });
    }

    if (mobilePrevPage) {
        mobilePrevPage.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                updateTable();
                updatePagination();
            }
        });
    }

    if (mobileNextPage) {
        mobileNextPage.addEventListener('click', function() {
            const totalPages = Math.ceil(filteredReservations.length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                updateTable();
                updatePagination();
            }
        });
    }

    // Logout button
    const logoutBtn = document.getElementById('logout-sidebar-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// Function to handle logout
function logout() {
    const token = localStorage.getItem('auth_token');

    if (token) {
        // Call logout API
        fetch('http://localhost:8000/api/auth/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            }
        })
        .then(() => {
            // Clear local storage regardless of API response
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_role');

            // Redirect to login page
            window.location.href = '../login.html';
        })
        .catch(error => {
            console.error('Logout error:', error);
            // Still clear local storage and redirect on error
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_role');
            window.location.href = '../login.html';
        });
    } else {
        // If no token, just redirect
        window.location.href = '../login.html';
    }
}
