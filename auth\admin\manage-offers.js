// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in and is an admin
    checkAdminAuth();

    // Initialize page
    initPage();

    // Add event listeners
    addEventListeners();
});

// Global variables
let allOffers = [];
let filteredOffers = [];
let currentPage = 1;
let itemsPerPage = 10;
let selectedOfferId = null;

// Function to check if user is logged in and is an admin
function checkAdminAuth() {
    const token = localStorage.getItem('auth_token');
    const userRole = localStorage.getItem('user_role');

    if (!token) {
        // User is not logged in, redirect to login page
        window.location.href = '../login.html';
        return;
    }

    if (userRole !== 'admin') {
        // User is not an admin, redirect to home page
        alert('Access denied: You do not have admin privileges.');
        window.location.href = '/index/index.html';
        return;
    }

    // User is logged in and is an admin, fetch user data
    fetchUserData();
}

// Function to fetch user data
function fetchUserData() {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (userData) {
        try {
            const user = JSON.parse(userData);
            const name = user.name || (user.data && user.data.name) || 'Admin';

            // Update admin name in sidebar
            const adminNameElement = document.getElementById('admin-name');
            if (adminNameElement) {
                adminNameElement.textContent = name;
            }
        } catch (error) {
            console.error('Error parsing user data:', error);
        }
    }

    // Refresh user data from API
    fetch('http://localhost:8000/api/auth/me', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch user data: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('User data fetched:', data);

        // Store updated user data
        localStorage.setItem('user_data', JSON.stringify(data));

        // Update admin name in sidebar
        const name = data.name || (data.data && data.data.name) || 'Admin';
        const adminNameElement = document.getElementById('admin-name');
        if (adminNameElement) {
            adminNameElement.textContent = name;
        }
    })
    .catch(error => {
        console.error('Error fetching user data:', error);
    });
}

// Function to initialize page
function initPage() {
    // Fetch all offers
    fetchOffers();
}

// Function to fetch offers
function fetchOffers() {
    const token = localStorage.getItem('auth_token');

    fetch('http://localhost:8000/api/offers', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to fetch offers: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Offers fetched:', data);

        // Extract offers data
        if (data.status === 'success' && data.data) {
            allOffers = data.data;
        } else if (Array.isArray(data)) {
            allOffers = data;
        } else if (data.data && Array.isArray(data.data)) {
            allOffers = data.data;
        } else {
            allOffers = [];
        }

        // Apply initial filter (show all)
        applyFilters();
    })
    .catch(error => {
        console.error('Error fetching offers:', error);

        // Show error message in table
        const offersTable = document.getElementById('offers-table');
        if (offersTable) {
            offersTable.innerHTML = `
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-red-500 dark:text-red-400">
                        Error loading offers: ${error.message}
                    </td>
                </tr>
            `;
        }
    });
}

// Function to apply filters
function applyFilters() {
    const searchTerm = document.getElementById('search').value.toLowerCase();
    const statusFilter = document.getElementById('status-filter').value;

    // Filter offers based on search term and status
    filteredOffers = allOffers.filter(offer => {
        // Search term filter
        const matchesSearch =
            (offer.titre && offer.titre.toLowerCase().includes(searchTerm)) ||
            (offer.destination && offer.destination.toLowerCase().includes(searchTerm)) ||
            (offer.description && offer.description.toLowerCase().includes(searchTerm));

        // Status filter
        let matchesStatus = true;
        if (statusFilter === 'active') {
            matchesStatus = offer.est_actif === true || offer.est_actif === 1;
        } else if (statusFilter === 'inactive') {
            matchesStatus = offer.est_actif === false || offer.est_actif === 0 || offer.est_actif === null;
        }

        return matchesSearch && matchesStatus;
    });

    // Reset to first page
    currentPage = 1;

    // Update table and pagination
    updateTable();
    updatePagination();
}

// Function to update table
function updateTable() {
    const offersTable = document.getElementById('offers-table');

    if (!offersTable) return;

    if (filteredOffers.length === 0) {
        offersTable.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    No offers found.
                </td>
            </tr>
        `;
        return;
    }

    // Calculate pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredOffers.length);
    const paginatedOffers = filteredOffers.slice(startIndex, endIndex);

    // Update pagination info
    document.getElementById('page-start').textContent = startIndex + 1;
    document.getElementById('page-end').textContent = endIndex;
    document.getElementById('total-items').textContent = filteredOffers.length;

    // Clear table
    offersTable.innerHTML = '';

    // Add offers to table
    paginatedOffers.forEach(offer => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150';

        // Format dates
        const startDate = new Date(offer.date_debut);
        const endDate = new Date(offer.date_fin);
        const formattedStartDate = startDate.toLocaleDateString();
        const formattedEndDate = endDate.toLocaleDateString();

        // Format price
        const price = parseFloat(offer.prix).toLocaleString('fr-FR', {
            style: 'currency',
            currency: 'EUR'
        });

        // Create status badge
        const statusBadge = offer.est_actif === true || offer.est_actif === 1
            ? '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300">Active</span>'
            : '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300">Inactive</span>';

        // Handle image path
        let imageUrl;
        if (offer.image) {
            // Check if the image path is already a full URL
            if (offer.image.startsWith('http://') || offer.image.startsWith('https://')) {
                imageUrl = offer.image;
            }
            // Check if it's a path in the storage directory
            else if (offer.image.includes('storage/offers/')) {
                imageUrl = `http://localhost:8000/${offer.image}`;
            }
            // Check if it's just the image filename
            else if (offer.image_path) {
                imageUrl = `http://localhost:8000/storage/offers/${offer.image_path}`;
            }
            // If it's a relative path without 'storage/offers/'
            else {
                imageUrl = `http://localhost:8000/storage/offers/${offer.image}`;
            }
        } else {
            // Default image if none provided
            imageUrl = 'https://via.placeholder.com/100x100?text=No+Image';
        }

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <img src="${imageUrl}" alt="${offer.titre || 'Offer'}" class="h-16 w-16 object-cover rounded-md">
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900 dark:text-white">${offer.titre || 'Untitled'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${offer.destination || 'N/A'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${price}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                ${statusBadge}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-400">${formattedStartDate} - ${formattedEndDate}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <a href="/offers/post-offer.html?edit=${offer.id}" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3">Edit</a>
                <a href="#" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 delete-offer" data-offer-id="${offer.id}">Delete</a>
            </td>
        `;

        offersTable.appendChild(row);
    });

    // Add event listeners to delete buttons
    document.querySelectorAll('.delete-offer').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            selectedOfferId = this.getAttribute('data-offer-id');
            showDeleteModal();
        });
    });
}

// Function to update pagination
function updatePagination() {
    const paginationContainer = document.getElementById('pagination');
    const totalPages = Math.ceil(filteredOffers.length / itemsPerPage);

    // Update mobile pagination buttons
    const mobilePrevPage = document.getElementById('mobile-prev-page');
    const mobileNextPage = document.getElementById('mobile-next-page');

    mobilePrevPage.disabled = currentPage === 1;
    mobileNextPage.disabled = currentPage === totalPages;

    // Update desktop pagination buttons
    const prevPage = document.getElementById('prev-page');
    const nextPage = document.getElementById('next-page');

    prevPage.disabled = currentPage === 1;
    nextPage.disabled = currentPage === totalPages;

    // Remove existing page buttons
    const pageButtons = paginationContainer.querySelectorAll('.page-button');
    pageButtons.forEach(button => button.remove());

    // Add page buttons
    for (let i = 1; i <= totalPages; i++) {
        const pageButton = document.createElement('button');
        pageButton.className = `page-button relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium ${
            i === currentPage
                ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30'
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
        }`;
        pageButton.textContent = i;
        pageButton.addEventListener('click', () => {
            currentPage = i;
            updateTable();
            updatePagination();
        });

        // Insert before the next button
        paginationContainer.insertBefore(pageButton, nextPage);
    }
}

// Function to show delete modal
function showDeleteModal() {
    const deleteModal = document.getElementById('delete-modal');
    deleteModal.classList.remove('hidden');
}

// Function to hide delete modal
function hideDeleteModal() {
    const deleteModal = document.getElementById('delete-modal');
    deleteModal.classList.add('hidden');
}

// Function to delete an offer
function deleteOffer() {
    if (!selectedOfferId) return;

    const token = localStorage.getItem('auth_token');

    fetch(`http://localhost:8000/api/offers/${selectedOfferId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Failed to delete offer: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Offer deleted:', data);

        // Hide modal
        hideDeleteModal();

        // Refresh offers
        fetchOffers();

        // Show success message
        alert('Offer deleted successfully!');
    })
    .catch(error => {
        console.error('Error deleting offer:', error);

        // Hide modal
        hideDeleteModal();

        // Show error message
        alert(`Error deleting offer: ${error.message}`);
    });
}

// Function to add event listeners
function addEventListeners() {
    // Search button
    const searchBtn = document.getElementById('search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', applyFilters);
    }

    // Search input (search on Enter key)
    const searchInput = document.getElementById('search');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    }

    // Status filter
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }

    // Pagination buttons
    const prevPage = document.getElementById('prev-page');
    const nextPage = document.getElementById('next-page');
    const mobilePrevPage = document.getElementById('mobile-prev-page');
    const mobileNextPage = document.getElementById('mobile-next-page');

    if (prevPage) {
        prevPage.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                updateTable();
                updatePagination();
            }
        });
    }

    if (nextPage) {
        nextPage.addEventListener('click', function() {
            const totalPages = Math.ceil(filteredOffers.length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                updateTable();
                updatePagination();
            }
        });
    }

    if (mobilePrevPage) {
        mobilePrevPage.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                updateTable();
                updatePagination();
            }
        });
    }

    if (mobileNextPage) {
        mobileNextPage.addEventListener('click', function() {
            const totalPages = Math.ceil(filteredOffers.length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                updateTable();
                updatePagination();
            }
        });
    }

    // Delete modal buttons
    const cancelDelete = document.getElementById('cancel-delete');
    const confirmDelete = document.getElementById('confirm-delete');

    if (cancelDelete) {
        cancelDelete.addEventListener('click', hideDeleteModal);
    }

    if (confirmDelete) {
        confirmDelete.addEventListener('click', deleteOffer);
    }

    // No need for add offer button event listener as we're using a direct link

    // Logout button
    const logoutBtn = document.getElementById('logout-sidebar-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// Function to handle logout
function logout() {
    const token = localStorage.getItem('auth_token');

    if (token) {
        // Call logout API
        fetch('http://localhost:8000/api/auth/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            }
        })
        .then(response => {
            // Clear local storage regardless of API response
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_role');

            // Redirect to login page
            window.location.href = '../login.html';
        })
        .catch(error => {
            console.error('Logout error:', error);
            // Still clear local storage and redirect on error
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_role');
            window.location.href = '../login.html';
        });
    } else {
        // If no token, just redirect
        window.location.href = '../login.html';
    }
}
