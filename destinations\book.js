    // Add debug info to help troubleshoot
        console.log("Page loaded at: " + new Date().toISOString());

        // Check if localStorage is available
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            console.log("localStorage is available");
        } catch (e) {
            console.error("localStorage is not available:", e);
            alert("Your browser's localStorage is disabled or not available. This feature is required for the hotel search to work properly across page refreshes.");
        }

        // Check if Google Maps API fails to load within 10 seconds
        window.googleMapsApiTimeout = setTimeout(function() {
            if (!window.google || !window.google.maps) {
                console.error('Google Maps API failed to load');
                const mapLoading = document.getElementById('map-loading');
                if (mapLoading) {
                    mapLoading.innerHTML = '<div class="text-center"><p class="text-red-600 font-medium">Failed to load Google Maps API.<br>Please refresh the page or check your internet connection.</p><button onclick="window.location.reload()" class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-200">Refresh Page</button></div>';
                }
            }
        }, 10000);