.offer-card:hover .offer-image {
            transform: scale(1.05);
            transition: transform 0.3s ease;
        }
        .offer-image {
            transition: transform 0.3s ease;
        }
        .discount-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            transform: rotate(15deg);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .search-box {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .parallax-bg {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }
        .price-tag {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 15px;
            border-radius: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
          .nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400;
        }
        .mobile-nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700;
        }

        /* Image Gallery Styles */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Loading states */
        .offers-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
        }

        /* Grid layout for offers */
        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .offer-image {
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        .offer-card:hover .offer-image {
            transform: scale(1.05);
        }

        /* Lightbox styles */
        #image-lightbox {
            backdrop-filter: blur(4px);
        }

        #lightbox-image {
            max-height: 90vh;
            max-width: 90vw;
        }

        /* Thumbnail styles */
        .thumbnail-container {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
        }

        .thumbnail-container::-webkit-scrollbar {
            height: 6px;
        }

        .thumbnail-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .thumbnail-container::-webkit-scrollbar-thumb {
            background-color: rgba(156, 163, 175, 0.5);
            border-radius: 3px;
        }

        .thumbnail-container::-webkit-scrollbar-thumb:hover {
            background-color: rgba(156, 163, 175, 0.7);
        }

        /* Loading animation */
        @keyframes shimmer {
            0% {
                background-position: -468px 0;
            }
            100% {
                background-position: 468px 0;
            }
        }

        .shimmer {
            animation: shimmer 1.5s ease-in-out infinite;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 400% 100%;
        }

        .dark .shimmer {
            background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
            background-size: 400% 100%;
        }