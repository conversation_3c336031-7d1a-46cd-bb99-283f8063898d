# Augment Prompt: Display Offer Images

## Context
I have a travel agency website with an offers system where users can view travel offers. Each offer can have multiple images stored in the backend. I need to implement a comprehensive image display system for offers that includes:

1. **Image Gallery Display** - Show multiple images for each offer
2. **Image Optimization** - Handle different image sizes and loading states
3. **Interactive Features** - Image zoom, carousel, lightbox functionality
4. **Responsive Design** - Works on all device sizes
5. **Error Handling** - Graceful fallbacks for missing images

## Current System
- **Backend API**: Laravel with offers stored in database
- **Image Storage**: Images stored in `public/storage/offers/` directory
- **API Endpoint**: `GET /api/offers/{offer}/images` returns offer images
- **Frontend**: HTML/CSS/JavaScript with Tailwind CSS
- **Authentication**: JWT token-based authentication

## API Response Format
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "offer_id": 12,
      "image_path": "public/storage/offers/N7Mbat6uRosrslvEzj7v7lzxkUDmQVKaUXKP9QmE.jpg",
      "alt_text": "Beautiful beach view",
      "is_primary": true,
      "created_at": "2024-01-15T10:30:00Z"
    },
    {
      "id": 2,
      "offer_id": 12,
      "image_path": "public/storage/offers/XyZ123AbC456DeF789GhI012JkL345MnO678PqR.jpg",
      "alt_text": "Hotel exterior",
      "is_primary": false,
      "created_at": "2024-01-15T10:31:00Z"
    }
  ]
}
```

## Requirements

### 1. **Offer Card Image Display**
- Show primary image as main thumbnail
- Display image count indicator (e.g., "5 photos")
- Hover effects to preview additional images
- Lazy loading for performance
- Placeholder for missing images

### 2. **Offer Detail Page Gallery**
- Full image carousel/slider
- Thumbnail navigation
- Zoom functionality
- Fullscreen lightbox
- Image navigation (previous/next)
- Touch/swipe support for mobile

### 3. **Image Loading & Optimization**
- Progressive loading with blur-up effect
- Multiple image sizes (thumbnail, medium, full)
- WebP format support with fallbacks
- Loading skeletons/placeholders
- Error handling for broken images

### 4. **Responsive Design**
- Mobile-first approach
- Touch gestures for mobile
- Adaptive image sizes
- Grid layout for multiple images
- Optimized for different screen sizes

### 5. **Accessibility**
- Proper alt text for all images
- Keyboard navigation support
- Screen reader compatibility
- Focus management in modals
- ARIA labels and roles

## Technical Specifications

### **File Structure**
```
offers/
├── offers.html (main offers listing)
├── offer-detail.html (individual offer page)
├── css/
│   └── image-gallery.css
├── js/
│   ├── image-gallery.js
│   ├── image-loader.js
│   └── lightbox.js
└── components/
    ├── offer-card.js
    └── image-carousel.js
```

### **Image URL Construction**
- Base URL: `http://localhost:8000/storage/offers/`
- Full path: `http://localhost:8000/storage/offers/{filename}`
- Thumbnail: `http://localhost:8000/storage/offers/thumbs/{filename}`

### **Styling Framework**
- Tailwind CSS for utility classes
- Custom CSS for complex animations
- Font Awesome for icons
- Dark mode support

### **JavaScript Features**
- Vanilla JavaScript (no jQuery)
- ES6+ features
- Async/await for API calls
- Event delegation
- Intersection Observer for lazy loading

## Desired Features

### **Basic Gallery**
- [x] Display primary image
- [x] Show image count
- [x] Basic carousel navigation
- [x] Responsive design

### **Advanced Features**
- [ ] Image zoom on hover/click
- [ ] Fullscreen lightbox modal
- [ ] Thumbnail strip navigation
- [ ] Swipe gestures for mobile
- [ ] Keyboard navigation (arrow keys)
- [ ] Image preloading
- [ ] Social sharing for images

### **Performance Optimizations**
- [ ] Lazy loading with Intersection Observer
- [ ] Image compression and WebP support
- [ ] Progressive image loading
- [ ] Caching strategies
- [ ] Optimized image sizes

### **User Experience**
- [ ] Smooth transitions and animations
- [ ] Loading states and skeletons
- [ ] Error handling with fallback images
- [ ] Touch-friendly controls
- [ ] Accessibility compliance

## Implementation Request

Please help me implement a complete image display system for offers that includes:

1. **Offer Cards Component** - Shows primary image with hover effects and image count
2. **Image Gallery Component** - Full carousel with navigation and zoom
3. **Lightbox Modal** - Fullscreen image viewing with navigation
4. **Image Loader Utility** - Handles loading, caching, and error states
5. **Responsive CSS** - Mobile-first design with smooth animations

The implementation should be:
- **Modern and performant** - Using latest web standards
- **Accessible** - Following WCAG guidelines
- **Mobile-friendly** - Touch gestures and responsive design
- **Consistent** - Matching the existing website design
- **Robust** - Proper error handling and fallbacks

Please provide complete, production-ready code with proper documentation and examples of how to integrate it with the existing offers system.

## Example Usage
```javascript
// Initialize image gallery for an offer
const gallery = new OfferImageGallery({
  offerId: 12,
  container: '#offer-gallery',
  apiUrl: 'http://localhost:8000/api/offers',
  authToken: localStorage.getItem('auth_token'),
  options: {
    showThumbnails: true,
    enableZoom: true,
    enableFullscreen: true,
    lazyLoad: true,
    autoPlay: false
  }
});

// Load and display images
await gallery.loadImages();
gallery.render();
```

This should create a professional, user-friendly image display system that enhances the overall user experience of the travel offers website.
