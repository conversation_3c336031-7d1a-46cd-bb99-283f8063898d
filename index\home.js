
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // User menu dropdown toggle
        const userMenuButton = document.querySelector('.user-menu-button');
        if (userMenuButton) {
            userMenuButton.addEventListener('click', function() {
                const dropdown = document.querySelector('.user-menu-dropdown');
                dropdown.classList.toggle('hidden');
            });

            // Close the dropdown when clicking outside
            document.addEventListener('click', function(event) {
                const dropdown = document.querySelector('.user-menu-dropdown');
                const userMenu = document.querySelector('.user-menu');

                if (!dropdown.classList.contains('hidden') &&
                    !userMenu.contains(event.target)) {
                    dropdown.classList.add('hidden');
                }
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    const mobileMenu = document.getElementById('mobile-menu');
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });

        // Date picker initialization (simplified for demo)
        document.getElementById('dates').addEventListener('focus', function() {
            this.type = 'date';
        });

        document.getElementById('hotel-dates').addEventListener('focus', function() {
            this.type = 'date';
        });

        // Simple form validation for newsletter
        const newsletterForm = document.querySelector('form');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = document.getElementById('email-address').value;
                if (email && email.includes('@')) {
                    alert('Merci pour votre inscription à notre newsletter !');
                    this.reset();
                }
            });
        }

        // Ajout d'un effet de scroll sur la navigation
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('nav');
            if (window.scrollY > 50) {
                nav.classList.add('scrolled-nav');
            } else {
                nav.classList.remove('scrolled-nav');
            }
        });

        // Animation au défilement
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.destination-card, .hotel-card, .offer-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            observer.observe(card);
        });

        // Ajout d'animations
        const animations = `
            @keyframes fade-in-up {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            .animate-fade-in-up {
                animation: fade-in-up 0.6s ease forwards;
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.innerText = animations;
        document.head.appendChild(styleSheet);
