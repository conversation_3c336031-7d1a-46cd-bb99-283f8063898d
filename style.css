
:root {
    --primary-color: #3b82f6;
    --primary-dark: #1d4ed8;
    --accent-color: #f59e0b;
    --gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}
@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes fadeOut {
    to { opacity: 0; visibility: hidden; }
}

@keyframes float {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes wave {
    0% { transform: translateX(0); }
    100% { transform: translateX(-25%); }
}

[data-animate] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

[data-animate].visible {
    opacity: 1;
    transform: translateY(0);
}

body {
    font-family: 'Poppins', sans-serif;
    scroll-behavior: smooth;
}

h1, h2, h3, h4, h5 {
    font-family: 'Playfair Display', serif;
}

.hero-gradient {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    position: relative;
}

.hero-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1528277342758-f1d7613953a2?ixlib=rb-4.0.3') no-repeat center center;
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.destination-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.destination-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.destination-card .card-image {
    height: 220px;
    transition: transform 0.6s ease;
}

.destination-card:hover .card-image {
    transform: scale(1.05);
}

.search-box {
    box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.15);
    border-radius: 1rem;
    backdrop-filter: blur(5px);
    background-color: rgba(255, 255, 255, 0.95);
}

.hotel-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-radius: 1rem;
    overflow: hidden;
    position: relative;
}

.hotel-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.hotel-card .hotel-image {
    transition: transform 0.6s ease;
}

/* Modern hotel card enhancements */
.hotel-card .group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
}

.hotel-card .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.hotel-card .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Modern hotel search results styling */
.hotel-search-header {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    position: relative;
    overflow: hidden;
}

.hotel-search-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

/* Enhanced no results state */
.no-results-state {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px dashed #cbd5e1;
    transition: all 0.3s ease;
}

.no-results-state:hover {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

/* Hotel card image placeholder */
.hotel-image-placeholder {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    position: relative;
}

.hotel-image-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(148, 163, 184, 0.1) 10px,
        rgba(148, 163, 184, 0.1) 20px
    );
}

.hotel-card .backdrop-blur-sm {
    backdrop-filter: blur(4px);
}

/* Gradient button effects */
.hotel-card button[type="submit"] {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    position: relative;
    overflow: hidden;
}

.hotel-card button[type="submit"]:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.4);
}

.hotel-card button[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.hotel-card button[type="submit"]:hover::before {
    left: 100%;
}

.hotel-card:hover .hotel-image {
    transform: scale(1.1);
}

/* Dark mode specific hotel card styles */
@media (prefers-color-scheme: dark) {
    .hotel-card {
        background: linear-gradient(145deg, #374151, #1f2937);
        border: 1px solid rgba(75, 85, 99, 0.3);
    }

    .hotel-card:hover {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
    }
}

/* Modern form input styles for hotel cards */
.hotel-card input[type="number"],
.hotel-card select,
.hotel-card textarea {
    transition: all 0.3s ease;
    border: 1.5px solid #e5e7eb;
}

.hotel-card input[type="number"]:focus,
.hotel-card select:focus,
.hotel-card textarea:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

/* Star rating animations */
.hotel-card .fa-star,
.hotel-card .fa-star-half-alt {
    transition: all 0.2s ease;
}

.hotel-card:hover .fa-star,
.hotel-card:hover .fa-star-half-alt {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(251, 191, 36, 0.3));
}

/* Badge animations */
.hotel-card .absolute {
    transition: all 0.3s ease;
}

.hotel-card:hover .absolute {
    transform: translateY(-2px);
}

/* Loading state for hotel cards */
.hotel-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.hotel-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Modern loading skeleton for hotel cards */
.hotel-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Enhanced search loading state */
.search-loading-modern {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border: 1px solid #bfdbfe;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
}

.search-loading-modern .spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Staggered animation for hotel cards */
.hotel-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.hotel-card:nth-child(1) { animation-delay: 0.1s; }
.hotel-card:nth-child(2) { animation-delay: 0.2s; }
.hotel-card:nth-child(3) { animation-delay: 0.3s; }
.hotel-card:nth-child(4) { animation-delay: 0.4s; }
.hotel-card:nth-child(5) { animation-delay: 0.5s; }
.hotel-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}

.btn-primary {
    background-color: var(--primary-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(29, 78, 216, 0.3);
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn-primary:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(29, 78, 216, 0.3);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 99;
}

.back-to-top.visible {
    opacity: 1;
    cursor: pointer;
}

.back-to-top:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
}

/* Form styles */
input[type="text"], input[type="email"], input[type="date"] {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input[type="text"]:focus, input[type="email"]:focus, input[type="date"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Rating stars animation */
.star-rating i {
    transition: transform 0.3s ease;
}

.hotel-card:hover .star-rating i {
    transform: scale(1.2);
    transition-delay: calc(0.05s * var(--i));
}

/* Custom badge styling */
.badge {
    padding: 0.35em 0.65em;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-discount {
    background-color: var(--accent-color);
    color: white;
}

/* Navigation active state */
.nav-link {
    position: relative;
    transition: all 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link.active::after {
    width: 100%;
}

/* Testimonial cards */
.testimonial-card {
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Quote styling */
.testimonial-card p::before {
    content: '"';
    font-size: 2rem;
    line-height: 0;
    vertical-align: -0.4em;
    margin-right: 0.2em;
    color: var(--primary-color);
    opacity: 0.5;
}

.testimonial-card p::after {
    content: '"';
    font-size: 2rem;
    line-height: 0;
    vertical-align: -0.4em;
    margin-left: 0.2em;
    color: var(--primary-color);
    opacity: 0.5;
}
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeOut 1s 2s forwards;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #fff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}


