


<?php

// Initialize variables to avoid undefined variable errors
$hotel_name = '';
$hotel_location = '';
$checkin = '';
$checkout = '';
$adults = 0;
$children = 0;
$infants = 0;
$rooms = 0;
$room_type = '';
$meal_plan = '';
$special_requests = '';
$message = '';
$message_type = '';

// Database insertion for hotel bookings
if (isset($_POST['hotel_submit'])) {
    // Get form data
    $hotel_name = $_POST['hotel_name'] ?? '';
    $hotel_location = $_POST['hotel_location'] ?? '';
    $checkin = $_POST['checkin'] ?? '';
    $checkout = $_POST['checkout'] ?? '';
    $adults = (int)($_POST['adults'] ?? 0);
    $children = (int)($_POST['children'] ?? 0);
    $infants = (int)($_POST['infants'] ?? 0);
    $rooms = (int)($_POST['rooms'] ?? 0);
    $room_type = $_POST['room_type'] ?? '';
    $meal_plan = $_POST['meal_plan'] ?? '';
    $special_requests = $_POST['special_requests'] ?? '';

    // استخراج بيانات المستخدم من الجلسة
    $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

    if ($hotel_name && $checkin && $checkout && $adults && $rooms && $room_type && $meal_plan) {
        // استعلام SQL لإدخال البيانات
        $sql = "INSERT INTO hotel_bookings (
            hotel_id,
            checkin,
            checkout,
            adults,
            children,
            infants,
            rooms,
            room_type,
            meal_plan,
            special_requests,
            user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($sql);
        // افترض أن hotel_id يتم الحصول عليه من البحث (يجب تعديله وفقاً لنظامك)
        $hotel_id = 1; // مثال - يجب استبداله بقيمة حقيقية
        $stmt->bind_param(
            "issiiiiissi",
            $hotel_id,
            $checkin,
            $checkout,
            $adults,
            $children,
            $infants,
            $rooms,
            $room_type,
            $meal_plan,
            $special_requests,
            $user_id
        );

        if ($stmt->execute()) {
            $message = "Hotel booking successful!";
            $message_type = "success";
        } else {
            $message = "Error: " . $conn->error;
            $message_type = "error";
        }
    }
}
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Pre-populate voyage packages (static array) with a fourth trip
$voyage_packages = [
    ['id' => 1, 'package' => 'Desert Safari Adventure', 'destination' => 'Saudi Arabia', 'description' => 'A thrilling 5-day desert safari in the Empty Quarter.', 'capacity' => 20, 'price' => 500.00, 'image' => 'https://via.placeholder.com/300x200?text=Desert+Safari'],
    ['id' => 2, 'package' => 'Istanbul Cultural Tour', 'destination' => 'Turkey', 'description' => 'Explore the rich history of Istanbul over 7 days.', 'capacity' => 15, 'price' => 700.00, 'image' => 'https://via.placeholder.com/300x200?text=Istanbul+Tour'],
    ['id' => 3, 'package' => 'Paris Romantic Getaway', 'destination' => 'France', 'description' => 'A 4-day romantic trip to Paris.', 'capacity' => 10, 'price' => 900.00, 'image' => 'https://via.placeholder.com/300x200?text=Paris+Getaway'],
    ['id' => 4, 'package' => 'Malaysian Rainforest Expedition', 'destination' => 'Malaysia', 'description' => 'A 6-day adventure into the lush rainforests of Borneo.', 'capacity' => 12, 'price' => 650.00, 'image' => 'https://via.placeholder.com/300x200?text=Malaysian+Rainforest']
];

// Simulate voyage bookings storage (static array)
$voyage_bookings = isset($_SESSION['voyage_bookings']) ? $_SESSION['voyage_bookings'] : [];

// Process form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Hotel booking form processing is now handled at the top of the file
    // This section now only handles saving to session (not database)
    if (isset($_POST['hotel_submit'])) {
        // We already have the variables from the top section

        if ($hotel_name && $checkin && $checkout && $adults && $rooms && $room_type && $meal_plan) {
            // Save booking to session for display purposes
            $hotel_booking = [
                'hotel_name' => $hotel_name,
                'hotel_location' => $hotel_location,
                'checkin' => $checkin,
                'checkout' => $checkout,
                'adults' => $adults,
                'children' => $children,
                'infants' => $infants,
                'rooms' => $rooms,
                'room_type' => $room_type,
                'meal_plan' => $meal_plan,
                'special_requests' => $special_requests
            ];
            $_SESSION['hotel_bookings'] = isset($_SESSION['hotel_bookings']) ? $_SESSION['hotel_bookings'] : [];
            $_SESSION['hotel_bookings'][] = $hotel_booking;

            // Message is already set in the database section
            if (empty($message)) {
                $message = "Hotel booking saved to session!";
                $message_type = "success";
            }
        } else if (empty($message)) {
            $message = "All required fields are mandatory.";
            $message_type = "error";
        }
    }

    if (isset($_POST['voyage_submit'])) {
        $package_id = (int)$_POST['package_id'];
        $depart_date = $_POST['depart_date'];
        $participants = (int)$_POST['participants'];
        $special_requests = $_POST['special_requests'];

        $trip = null;
        foreach ($voyage_packages as $pkg) {
            if ($pkg['id'] == $package_id) {
                $trip = $pkg;
                break;
            }
        }

        if (!$trip) {
            $message = "Invalid package selected.";
            $message_type = "error";
        } else {
            $destination = $trip['destination'] ?? 'Unknown';
            $capacity = $trip['capacity'] ?? 20;

            $booked = 0;
            foreach ($voyage_bookings as $booking) {
                if ($booking['package'] === $trip['package'] && $booking['depart_date'] === $depart_date) {
                    $booked += $booking['participants'];
                }
            }
            $available = $capacity - $booked;

            if ($participants > 20) {
                $message = "Maximum 20 participants allowed.";
                $message_type = "error";
            } else if ($available >= $participants) {
                $voyage_bookings[] = [
                    'package' => $trip['package'],
                    'destination' => $destination,
                    'depart_date' => $depart_date,
                    'participants' => $participants,
                    'special_requests' => $special_requests
                ];
                $_SESSION['voyage_bookings'] = $voyage_bookings;
                $message = "Trip booking successful!";
                $message_type = "success";
            } else if ($available > 0) {
                $message = "Only $available place(s) left.";
                $message_type = "warning";
            } else {
                $message = "This trip is fully booked.";
                $message_type = "error";
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#ffffff">
    <title>Destinations - Mojo Company</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../style.css">
    <script>
        // Tailwind configuration for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400;
        }
        .mobile-nav-link {
            @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700;
        }
        .tab-button.active {
            @apply text-blue-600 border-blue-600 dark:text-blue-400 dark:border-blue-400;
        }
        .tab-button {
            @apply text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400;
        }

        /* Google Maps styling */
        #map {
            height: 400px;
            width: 100%;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        #map-container {
            height: 400px;
            width: 100%;
            border-radius: 12px;
            overflow: hidden;
        }
    </style>
    <!-- Load Navbar Script -->
    <script src="/components/load-navbar.js"></script>
</head>
<body class="font-sans antialiased text-gray-800 dark:text-white dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <?php if (!empty($message)) { ?>
        <div class="fixed top-4 right-4 bg-<?php echo $message_type === 'success' ? 'green' : ($message_type === 'warning' ? 'yellow' : 'red'); ?>-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center">
            <?php echo htmlspecialchars($message); ?>
            <button onclick="this.parentElement.style.display='none';" class="ml-2 text-sm">✕</button>
        </div>
    <?php } ?>

    <!-- Hero Section -->
    <div class="hero-gradient text-white">
        <div class="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-extrabold tracking-tight sm:text-5xl lg:text-6xl">
                    Destinations & Voyages
                </h1>
                <p class="mt-6 max-w-lg mx-auto text-xl">
                    Découvrez les meilleurs hôtels et voyages organisés pour vos prochaines vacances.
                </p>
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 flex-1">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
            <div class="flex space-x-4 border-b border-gray-200 dark:border-gray-700">
                <button id="hotel-btn" class="tab-button px-4 py-2 border-b-2 active font-semibold">
                    <i class="fas fa-hotel mr-2"></i>Hotels
                </button>
                <button id="voyage-btn" class="tab-button px-4 py-2 font-semibold">
                    <i class="fas fa-map-marked-alt mr-2"></i>Voyage Organisé
                </button>
            </div>

            <div id="hotel-section" class="tab-content mt-6">
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-search mr-2 text-blue-600 dark:text-blue-400"></i>
                        Rechercher des hôtels
                    </h3>
                    <form id="hotel-search-form" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    <i class="fas fa-map-marker-alt mr-1 text-blue-600 dark:text-blue-400"></i>
                                    Destination
                                </label>
                                <input type="text" id="location" name="location"
                                       class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                                              focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                                              dark:bg-gray-800 dark:text-white dark:focus:ring-blue-400"
                                       placeholder="Entrez une ville ou une région">
                            </div>
                            <div>
                                <label for="radius" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    <i class="fas fa-circle-notch mr-1 text-blue-600 dark:text-blue-400"></i>
                                    Rayon de recherche (km)
                                </label>
                                <input type="number" id="radius" name="radius" value="10" min="1" max="50"
                                       class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                                              focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                                              dark:bg-gray-800 dark:text-white dark:focus:ring-blue-400">
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="checkin" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    <i class="fas fa-calendar-alt mr-1 text-blue-600 dark:text-blue-400"></i>
                                    Arrivée
                                </label>
                                <input type="date" id="checkin" name="checkin" required
                                       class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                                              focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                                              dark:bg-gray-800 dark:text-white dark:focus:ring-blue-400"
                                       min="<?php echo date('Y-m-d'); ?>">
                            </div>
                            <div>
                                <label for="checkout" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    <i class="fas fa-calendar-alt mr-1 text-blue-600 dark:text-blue-400"></i>
                                    Départ
                                </label>
                                <input type="date" id="checkout" name="checkout" required
                                       class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                                              focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                                              dark:bg-gray-800 dark:text-white dark:focus:ring-blue-400"
                                       min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button type="button" onclick="searchHotelsFromForm()"
                                    class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg
                                           transition duration-300 font-medium flex items-center justify-center">
                                <i class="fas fa-search mr-2"></i>Rechercher
                            </button>
                            <button type="button" onclick="clearHotelSearch()"
                                    class="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300
                                           hover:bg-gray-400 dark:hover:bg-gray-500 px-6 py-3 rounded-lg
                                           transition duration-300 font-medium flex items-center justify-center">
                                <i class="fas fa-times mr-2"></i>Effacer
                            </button>
                        </div>
                    </form>
                </div>
                <div class="mt-6">
                    <div id="map-container" class="relative">
                        <div id="map"></div>
                        <div id="map-loading" class="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-70">
                            <div class="text-center">
                                <div class="spinner mx-auto mb-2"></div>
                                <p class="text-indigo-600 font-medium">Loading map...</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="search-loading" class="hidden text-center mt-4 flex items-center justify-center">
                    <div class="spinner mr-2"></div>
                    <p class="text-indigo-600 font-medium">Searching for hotels...</p>
                </div>
                <div id="hotel-results" class="mt-6"></div>
            </div>

            <div id="voyage-section" class="tab-content hidden mt-6">
                <div class="mb-6">
                    <div class="flex flex-wrap gap-2 mb-2">
                        <button type="button" onclick="filterVoyages('')" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-200">All Destinations</button>
                        <?php
                        $unique_destinations = array_unique(array_column($voyage_packages, 'destination'));
                        foreach ($unique_destinations as $dest) { ?>
                            <button type="button" onclick="filterVoyages('<?php echo htmlspecialchars($dest); ?>')" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition duration-200"><?php echo htmlspecialchars($dest); ?></button>
                        <?php } ?>
                    </div>
                    <button type="button" onclick="clearVoyageFilters()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition duration-200">Clear Filters</button>
                </div>
                <div id="voyage-packages" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
                    <?php foreach ($voyage_packages as $package) { ?>
                        <div class="voyage-card bg-white rounded-xl shadow-md overflow-hidden" data-destination="<?php echo htmlspecialchars($package['destination']); ?>">
                            <img src="<?php echo htmlspecialchars($package['image']); ?>" alt="<?php echo htmlspecialchars($package['package']); ?>" class="w-full h-64 object-cover">
                            <div class="p-6">
                                <h4 class="text-xl font-semibold text-gray-800"><?php echo htmlspecialchars($package['package']); ?></h4>
                                <p class="text-gray-600 text-sm mt-2"><?php echo htmlspecialchars($package['destination']); ?></p>
                                <p class="text-gray-600 text-sm mt-2"><?php echo htmlspecialchars($package['description']); ?></p>
                                <p class="text-gray-600 text-sm mt-2">Capacity: <?php echo $package['capacity']; ?> people</p>
                                <p class="text-green-600 font-semibold mt-2 text-lg">$<?php echo number_format($package['price'], 2); ?>/person</p>
                                <button onclick="showVoyageForm(<?php echo $package['id']; ?>, '<?php echo htmlspecialchars($package['package']); ?>')" class="mt-4 w-full bg-indigo-600 text-white py-3 rounded-lg hover:bg-indigo-700 transition duration-200 font-semibold">Book Now</button>
                            </div>
                        </div>
                    <?php } ?>
                </div>
                <div id="voyage-form" class="hidden mt-8">
                    <form method="POST" action="" class="space-y-6 bg-white p-6 rounded-xl shadow-md">
                        <input type="hidden" id="package_id" name="package_id">
                        <div>
                            <label for="package_name" class="form-label block text-sm mb-1">Trip Package</label>
                            <input type="text" id="package_name" readonly class="form-input w-full p-3 border border-gray-300 rounded-lg bg-gray-100">
                        </div>
                        <div>
                            <label for="depart_date" class="form-label block text-sm mb-1">Departure Date</label>
                            <input type="date" id="depart_date" name="depart_date" required class="form-input w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500" min="<?php echo date('Y-m-d'); ?>">
                        </div>
                        <div>
                            <label for="participants" class="form-label block text-sm mb-1">Participants</label>
                            <input type="number" id="participants" name="participants" min="1" max="20" required class="form-input w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="special_requests" class="form-label block text-sm mb-1">Special Requests</label>
                            <textarea id="special_requests" name="special_requests" class="form-input w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500"></textarea>
                        </div>
                        <button type="submit" name="voyage_submit" class="w-full bg-indigo-600 text-white p-3 rounded-lg hover:bg-indigo-700 transition duration-200 font-semibold">Confirm Booking</button>
                    </form>
                </div>
                <?php if (!empty($voyage_bookings)): ?>
                    <div class="booked-trips mt-8">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Your Booked Trips</h3>
                        <ul class="list-disc list-inside space-y-2 text-gray-700">
                            <?php foreach ($voyage_bookings as $index => $booking): ?>
                                <li>
                                    <?php echo htmlspecialchars($booking['package']); ?> -
                                    Departure: <?php echo htmlspecialchars($booking['depart_date']); ?> -
                                    Participants: <?php echo htmlspecialchars($booking['participants']); ?>
                                    <?php if (!empty($booking['special_requests'])): ?>
                                        - Requests: <?php echo htmlspecialchars($booking['special_requests']); ?>
                                    <?php endif; ?>
                                    <button onclick="removeBooking(<?php echo $index; ?>)" class="text-red-500 hover:text-red-700 ml-2">Remove</button>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <footer class="bg-indigo-600 text-white p-4 text-center">
        <p>© 2025 Travel Booking. All rights reserved.</p>
    </footer>

    <!-- Add debug info and fallback mechanism for Google Maps API -->
    <script src="book.js"></script>

    <!-- Load hotel.js first so initMap is defined before Google Maps API loads -->
    <script src="hotel.js"></script>

    <!-- Load Google Maps API -->
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBrmugPh_qWte3EXyNH9GKg4dRuaGwyCUc&libraries=places&callback=initMap" async defer onerror="alert('Failed to load Google Maps API. Please check your internet connection and try again.')"></script>

    <script src="script.js"></script>
</body>
</html>