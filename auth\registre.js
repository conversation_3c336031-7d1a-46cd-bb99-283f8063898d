


        // Initialize AOS animation library
        AOS.init({
            once: false
        });

        // Dark mode functionality
        const themeToggleBtn = document.getElementById('theme-toggle');
        const themeToggleBtnMobile = document.getElementById('theme-toggle-mobile');
        const themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
        const themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');
        const themeToggleDarkIconMobile = document.getElementById('theme-toggle-dark-icon-mobile');
        const themeToggleLightIconMobile = document.getElementById('theme-toggle-light-icon-mobile');

        // Change the icons inside the button based on previous settings
        if (localStorage.getItem('color-theme') === 'dark' ||
            (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark');
            themeToggleLightIcon.classList.add('hidden');
            themeToggleDarkIcon.classList.remove('hidden');
            themeToggleLightIconMobile.classList.add('hidden');
            themeToggleDarkIconMobile.classList.remove('hidden');
        } else {
            document.documentElement.classList.remove('dark');
            document.body.classList.remove('dark');
            themeToggleDarkIcon.classList.add('hidden');
            themeToggleLightIcon.classList.remove('hidden');
            themeToggleDarkIconMobile.classList.add('hidden');
            themeToggleLightIconMobile.classList.remove('hidden');
        }

        function toggleTheme() {
            // Toggle icons
            themeToggleDarkIcon.classList.toggle('hidden');
            themeToggleLightIcon.classList.toggle('hidden');
            themeToggleDarkIconMobile.classList.toggle('hidden');
            themeToggleLightIconMobile.classList.toggle('hidden');

            // Add rotation animation
            if (themeToggleDarkIcon.classList.contains('hidden')) {
                themeToggleLightIcon.classList.add('rotate');
                themeToggleLightIconMobile.classList.add('rotate');
            } else {
                themeToggleDarkIcon.classList.add('rotate');
                themeToggleDarkIconMobile.classList.add('rotate');
            }

            // Remove rotation animation after it completes
            setTimeout(() => {
                themeToggleLightIcon.classList.remove('rotate');
                themeToggleDarkIcon.classList.remove('rotate');
                themeToggleLightIconMobile.classList.remove('rotate');
                themeToggleDarkIconMobile.classList.remove('rotate');
            }, 500);

            // Toggle dark mode class on document
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                document.body.classList.remove('dark');
                localStorage.setItem('color-theme', 'light');
            } else {
                document.documentElement.classList.add('dark');
                document.body.classList.add('dark');
                localStorage.setItem('color-theme', 'dark');
            }
        }

        // Add event listeners to toggle buttons
        themeToggleBtn.addEventListener('click', toggleTheme);
        themeToggleBtnMobile.addEventListener('click', toggleTheme);

        // Mobile menu toggle with animation
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');

            if (!menu.classList.contains('hidden')) {
                menu.classList.add('animate__fadeIn');
                menu.classList.remove('animate__fadeOut');
            } else {
                menu.classList.add('animate__fadeOut');
                menu.classList.remove('animate__fadeIn');
            }
        });

        // Form validation with animations
        const registerForm = document.querySelector('form');
        if (registerForm) {
            // Add animation to inputs on focus
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.classList.add('animate__animated', 'animate__pulse');
                    setTimeout(() => {
                        this.classList.remove('animate__animated', 'animate__pulse');
                    }, 1000);

                    // Cacher le message d'erreur quand l'utilisateur commence à taper
                    const errorContainer = document.getElementById('register-error');
                    if (errorContainer) {
                        errorContainer.classList.add('hidden');
                    }
                });

                // Cacher également le message d'erreur quand l'utilisateur tape
                input.addEventListener('input', function() {
                    const errorContainer = document.getElementById('register-error');
                    if (errorContainer) {
                        errorContainer.classList.add('hidden');
                    }

                    // Supprimer la classe d'erreur
                    this.classList.remove('border-red-500');
                });
            });

            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const firstName = document.getElementById('first-name').value;
                const lastName = document.getElementById('last-name').value;
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm-password').value;
                const terms = document.getElementById('terms').checked;
                const newsletter = document.getElementById('newsletter').checked;

                // Fonction pour afficher les erreurs
                const showError = (message) => {
                    const errorContainer = document.getElementById('register-error');
                    errorContainer.textContent = message;
                    errorContainer.classList.remove('hidden');

                    // Faire défiler vers le haut pour voir le message d'erreur
                    errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                };

                // Fonction pour cacher les erreurs
                const hideError = () => {
                    const errorContainer = document.getElementById('register-error');
                    errorContainer.classList.add('hidden');
                };

                // Add shake animation to inputs with errors
                const validateInput = (input, condition, message) => {
                    if (!condition) {
                        input.classList.add('animate__animated', 'animate__shakeX', 'border-red-500');
                        setTimeout(() => {
                            input.classList.remove('animate__animated', 'animate__shakeX');
                        }, 1000);
                        showError(message);
                        return false;
                    }
                    input.classList.remove('border-red-500');
                    return true;
                };

                const firstNameInput = document.getElementById('first-name');
                const lastNameInput = document.getElementById('last-name');
                const emailInput = document.getElementById('email');
                const passwordInput = document.getElementById('password');
                const confirmPasswordInput = document.getElementById('confirm-password');
                const termsInput = document.getElementById('terms');

                // Cacher les erreurs précédentes
                hideError();

                // Valider les entrées
                if (!validateInput(firstNameInput, firstName, 'Veuillez entrer votre prénom')) return;
                if (!validateInput(lastNameInput, lastName, 'Veuillez entrer votre nom')) return;
                if (!validateInput(emailInput, email && email.includes('@'), 'Veuillez entrer une adresse email valide')) return;
                if (!validateInput(passwordInput, password && password.length >= 8, 'Le mot de passe doit contenir au moins 8 caractères')) return;
                if (!validateInput(confirmPasswordInput, password === confirmPassword, 'Les mots de passe ne correspondent pas')) return;
                if (!validateInput(termsInput, terms, 'Vous devez accepter les conditions générales')) return;

                // Désactiver le bouton de soumission et afficher un indicateur de chargement
                const submitButton = document.querySelector('button[type="submit"]');
                const originalButtonText = submitButton.innerHTML;
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Inscription en cours...';

                // Préparer les données pour l'API
                const userData = {
                    name: firstName + ' ' + lastName,
                    email: email,
                    password: password,
                    password_confirmation: confirmPassword,
                    newsletter: newsletter
                };

                // Appel à l'API d'inscription
                fetch('http://localhost:8000/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(userData)
                })
                .then(response => {
                    if (!response.ok) {
                        // Essayer de lire le message d'erreur du serveur
                        return response.json().then(errorData => {
                            throw new Error(
                                errorData.message ||
                                (errorData.errors ? Object.values(errorData.errors).flat().join(', ') : 'Erreur lors de l\'inscription')
                            );
                        }).catch(err => {
                            if (err instanceof SyntaxError) {
                                throw new Error(`Erreur serveur (${response.status})`);
                            }
                            throw err;
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Registration successful:', data);

                    // Afficher une notification de succès
                    showNotification('Inscription réussie ! Redirection vers la page de connexion...', 'success');

                    // Success animation
                    const formContainer = document.querySelector('.card-lift');
                    formContainer.classList.add('animate__animated', 'animate__fadeOutUp');

                    // Redirection après animation
                    setTimeout(() => {
                        // Stocker un message de succès pour la page de connexion
                        localStorage.setItem('registration_success', 'true');
                        window.location.href = 'login.html';
                    }, 1500);
                })
                .catch(error => {
                    console.error('Registration error:', error);

                    // Réactiver le bouton de soumission
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalButtonText;

                    // Afficher l'erreur dans le conteneur dédié
                    showError('Échec de l\'inscription: ' + error.message);

                    // Afficher également une notification
                    showNotification('Échec de l\'inscription: ' + error.message, 'error');
                });
            });
        }

        // Fonction pour afficher des notifications stylisées
        function showNotification(message, type) {
            // Supprimer les notifications existantes
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());

            // Créer une nouvelle notification
            const notification = document.createElement('div');
            notification.className = `notification fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg animate__animated animate__fadeInDown z-50 ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;

            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
                    <p>${message}</p>
                </div>
            `;

            document.body.appendChild(notification);

            // Supprimer la notification après 3 secondes
            setTimeout(() => {
                notification.classList.remove('animate__fadeInDown');
                notification.classList.add('animate__fadeOutUp');
                setTimeout(() => {
                    notification.remove();
                }, 1000);
            }, 3000);
        }

        // Add subtle parallax effect to background elements
        if (window.innerWidth > 768) {
            document.addEventListener('mousemove', function(e) {
                const moveX = (e.clientX - window.innerWidth / 2) / 50;
                const moveY = (e.clientY - window.innerHeight / 2) / 50;

                const elements = document.querySelectorAll('.float-animation');
                elements.forEach(el => {
                    el.style.transform = `translate(${moveX}px, ${moveY}px)`;
                });
            });
        }
