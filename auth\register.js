// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is already logged in
    const token = localStorage.getItem('auth_token');
    if (token) {
        // Redirect to appropriate page based on role
        const userRole = localStorage.getItem('user_role');
        if (userRole === 'admin') {
            window.location.href = 'admin/dashboard.html';
        } else {
            window.location.href = '../index/index.html';
        }
    }
    
    // Form validation and submission
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const passwordConfirmation = document.getElementById('password_confirmation').value;
            const phone = document.getElementById('phone')?.value || '';
            const terms = document.getElementById('terms').checked;
            
            // Validate form
            if (!validateForm(name, email, password, passwordConfirmation, terms)) {
                return;
            }
            
            // Show loading state
            const registerButton = document.getElementById('register-button');
            const originalButtonText = registerButton.innerHTML;
            registerButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Registering...';
            registerButton.disabled = true;
            
            // Call the registration API
            registerUser(name, email, password, passwordConfirmation, phone)
                .then(response => {
                    // Show success message
                    showSuccess('Registration successful! Redirecting to login page...');
                    
                    // Set registration success flag for login page
                    localStorage.setItem('registration_success', 'true');
                    
                    // Redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                })
                .catch(error => {
                    // Show error message
                    showError(error.message || 'Registration failed. Please try again.');
                    
                    // Reset button
                    registerButton.innerHTML = originalButtonText;
                    registerButton.disabled = false;
                });
        });
    }
});

// Validate form inputs
function validateForm(name, email, password, passwordConfirmation, terms) {
    // Reset previous error messages
    hideError();
    
    // Validate name
    if (!name || name.trim().length < 2) {
        showError('Please enter your full name (at least 2 characters)');
        return false;
    }
    
    // Validate email
    if (!email || !isValidEmail(email)) {
        showError('Please enter a valid email address');
        return false;
    }
    
    // Validate password
    if (!password || password.length < 8) {
        showError('Password must be at least 8 characters long');
        return false;
    }
    
    // Validate password confirmation
    if (password !== passwordConfirmation) {
        showError('Passwords do not match');
        return false;
    }
    
    // Validate terms
    if (!terms) {
        showError('You must agree to the Terms of Service and Privacy Policy');
        return false;
    }
    
    return true;
}

// Check if email is valid
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Register user with API
async function registerUser(name, email, password, password_confirmation, phone) {
    try {
        const response = await fetch('http://localhost:8000/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                name,
                email,
                password,
                password_confirmation,
                phone
            })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(
                errorData.message || 
                (errorData.errors ? Object.values(errorData.errors).flat().join(', ') : 'Registration failed')
            );
        }
        
        return await response.json();
    } catch (error) {
        console.error('Registration error:', error);
        throw error;
    }
}

// Show error message
function showError(message) {
    const errorContainer = document.getElementById('register-error');
    if (errorContainer) {
        errorContainer.textContent = message;
        errorContainer.classList.remove('hidden');
        
        // Scroll to error message
        errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

// Hide error message
function hideError() {
    const errorContainer = document.getElementById('register-error');
    if (errorContainer) {
        errorContainer.classList.add('hidden');
    }
}

// Show success message
function showSuccess(message) {
    // Hide any error messages
    hideError();
    
    // Create a success notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 bg-green-500 text-white animate__animated animate__fadeInDown';
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <p>${message}</p>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('animate__fadeInDown');
        notification.classList.add('animate__fadeOutUp');
        setTimeout(() => {
            notification.remove();
        }, 1000);
    }, 3000);
}

// Add test credentials button (for development only)
window.addEventListener('load', () => {
    // Function to pre-fill test credentials
    const fillTestCredentials = () => {
        document.getElementById('name').value = 'Test User';
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('password').value = 'password123';
        document.getElementById('password_confirmation').value = 'password123';
        document.getElementById('terms').checked = true;
    };
    
    // Add a discreet button to fill test credentials
    const testButton = document.createElement('button');
    testButton.type = 'button';
    testButton.className = 'text-xs text-gray-400 hover:text-gray-600 absolute bottom-2 right-2 opacity-50';
    testButton.textContent = 'Test';
    testButton.addEventListener('click', fillTestCredentials);
    
    const container = document.querySelector('.max-w-md');
    if (container) {
        container.appendChild(testButton);
    }
});
