<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Offers API</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-8">
    <h1 class="text-2xl font-bold mb-4">Test Offers API</h1>
    
    <div id="test-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Offers will be loaded here -->
    </div>
    
    <div id="debug-info" class="mt-8 p-4 bg-gray-100 rounded">
        <h2 class="font-bold">Debug Info:</h2>
        <div id="debug-content"></div>
    </div>

    <script src="../offers.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            const debugContent = document.getElementById('debug-content');
            const container = document.getElementById('test-container');
            
            try {
                debugContent.innerHTML += '<p>DOM loaded</p>';
                debugContent.innerHTML += '<p>OffersAPI available: ' + (typeof OffersAPI) + '</p>';
                
                if (typeof OffersAPI === 'undefined') {
                    throw new Error('OffersAPI not available');
                }
                
                const api = new OffersAPI('http://localhost:8000/api');
                debugContent.innerHTML += '<p>API instance created</p>';
                
                const offers = await api.getAllOffers();
                debugContent.innerHTML += '<p>Offers fetched: ' + offers.length + '</p>';
                
                const limitedOffers = offers.slice(0, 3);
                debugContent.innerHTML += '<p>Limited to 3 offers</p>';
                
                // Display offers
                limitedOffers.forEach(offer => {
                    const card = document.createElement('div');
                    card.className = 'bg-white p-4 rounded shadow';
                    card.innerHTML = `
                        <h3 class="font-bold">${offer.titre || 'No title'}</h3>
                        <p class="text-gray-600">${offer.description || 'No description'}</p>
                        <p class="text-blue-600">€${offer.prix || 'N/A'}</p>
                    `;
                    container.appendChild(card);
                });
                
                debugContent.innerHTML += '<p>Offers displayed successfully</p>';
                
            } catch (error) {
                debugContent.innerHTML += '<p class="text-red-600">Error: ' + error.message + '</p>';
                console.error('Test error:', error);
            }
        });
    </script>
</body>
</html>
