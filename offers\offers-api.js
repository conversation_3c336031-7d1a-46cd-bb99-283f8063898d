/**
 * Offers API Integration for offers.html
 * Fetches and displays offers from http://localhost:8000/api/offers
 */

// Initialize the OffersAPI instance
const offersAPI = new OffersAPI('http://localhost:8000/api');

// DOM elements
let offersContainer;
let loadingElement;
let errorElement;
let searchBtn;
let destinationInput;
let dateInput;
let priceRangeSelect;
let filterButtons;
let loadMoreBtn;

// State variables
let allOffers = [];
let filteredOffers = [];
let displayedOffers = [];
let currentPage = 1;
const offersPerPage = 9;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    setupEventListeners();
    loadOffers();
    checkLastReservationStatus();
});

/**
 * Initialize DOM elements
 */
function initializeElements() {
    offersContainer = document.getElementById('offers-container');
    loadingElement = document.getElementById('loading');
    errorElement = document.getElementById('error');
    searchBtn = document.getElementById('search-btn');
    destinationInput = document.getElementById('destination-input');
    dateInput = document.getElementById('date-input');
    priceRangeSelect = document.getElementById('price-range');
    filterButtons = document.querySelectorAll('.filter-btn');
    loadMoreBtn = document.getElementById('load-more-btn');
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Search functionality
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }

    // Filter buttons
    filterButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Update active filter button
            filterButtons.forEach(b => {
                b.classList.remove('bg-blue-100', 'dark:bg-blue-800', 'text-blue-700', 'dark:text-blue-200');
                b.classList.add('hover:bg-blue-100', 'dark:hover:bg-blue-800', 'hover:text-blue-700', 'dark:hover:text-blue-200', 'dark:text-gray-300');
            });

            this.classList.add('bg-blue-100', 'dark:bg-blue-800', 'text-blue-700', 'dark:text-blue-200');
            this.classList.remove('hover:bg-blue-100', 'dark:hover:bg-blue-800', 'hover:text-blue-700', 'dark:hover:text-blue-200', 'dark:text-gray-300');

            // Apply filter
            const filterType = this.textContent.toLowerCase();
            applyFilter(filterType);
        });
    });

    // Load more button
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreOffers);
    }

    // Enter key for search
    if (destinationInput) {
        destinationInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }
}

/**
 * Load offers from API
 */
async function loadOffers() {
    try {
        showLoading();
        hideError();

        console.log('Loading offers from API...');
        allOffers = await offersAPI.getAllOffers();
        console.log('Loaded offers:', allOffers);

        filteredOffers = [...allOffers];
        currentPage = 1;
        displayOffers();

    } catch (error) {
        console.error('Error loading offers:', error);
        showError('Failed to load offers. Please try again later.');
    } finally {
        hideLoading();
    }
}

/**
 * Display offers with pagination
 */
function displayOffers() {
    if (!offersContainer) return;

    // Calculate offers to display
    const startIndex = 0;
    const endIndex = currentPage * offersPerPage;
    displayedOffers = filteredOffers.slice(startIndex, endIndex);

    // Clear container
    offersContainer.innerHTML = '';

    if (displayedOffers.length === 0) {
        offersContainer.innerHTML = `
            <div class="col-span-full text-center py-12">
                <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">No offers found</h3>
                <p class="text-gray-500 dark:text-gray-500">Try adjusting your search criteria or filters.</p>
            </div>
        `;
        hideLoadMoreButton();
        return;
    }

    // Display offers
    displayedOffers.forEach(offer => {
        const offerCard = createOfferCard(offer);
        offersContainer.appendChild(offerCard);
    });

    // Show/hide load more button
    if (endIndex < filteredOffers.length) {
        showLoadMoreButton();
    } else {
        hideLoadMoreButton();
    }
}

/**
 * Create offer card element
 */
function createOfferCard(offer) {
    const card = document.createElement('div');
    card.className = 'bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transform transition duration-300 hover:scale-105 hover:shadow-xl';

    // Get first image or use placeholder
    const firstImage = offer.images && offer.images.length > 0 ? offer.images[0] : null;
    console.log('Offer:', offer.titre, 'Images:', offer.images, 'First image:', firstImage);
    const imageUrl = firstImage ? getImageUrl(firstImage.image_path) : 'https://via.placeholder.com/400x250?text=No+Image';
    console.log('Generated image URL:', imageUrl);

    // Format price
    const price = offer.prix ? `€${offer.prix}` : 'Price on request';

    // Format dates
    const startDate = offer.date_debut ? new Date(offer.date_debut).toLocaleDateString() : '';
    const endDate = offer.date_fin ? new Date(offer.date_fin).toLocaleDateString() : '';
    const dateRange = startDate && endDate ? `${startDate} - ${endDate}` : 'Dates TBD';

    card.innerHTML = `
        <div class="relative">
            <img src="${imageUrl}" alt="${offer.titre || 'Offer'}"
                 class="w-full h-48 object-cover"
                 onerror="this.src='https://via.placeholder.com/400x250?text=Image+Not+Found'">

            ${offer.images && offer.images.length > 1 ? `
                <div class="absolute top-3 right-3 bg-black bg-opacity-50 text-white px-2 py-1 rounded-full text-sm">
                    <i class="fas fa-images mr-1"></i>${offer.images.length}
                </div>
            ` : ''}

            ${offer.est_actif === 0 ? `
                <div class="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-medium">
                    Inactive
                </div>
            ` : ''}
        </div>

        <div class="p-6">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2 line-clamp-2">
                ${offer.titre || 'Untitled Offer'}
            </h3>

            <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                ${offer.description || 'No description available.'}
            </p>

            <div class="space-y-2 mb-4">
                ${offer.destination ? `
                    <div class="flex items-center text-gray-600 dark:text-gray-400">
                        <i class="fas fa-map-marker-alt mr-2 text-blue-500"></i>
                        <span class="text-sm">${offer.destination}</span>
                    </div>
                ` : ''}

                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-calendar-alt mr-2 text-blue-500"></i>
                    <span class="text-sm">${dateRange}</span>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    ${price}
                </div>

                <div class="flex space-x-2">
                    <button onclick="viewOfferDetails(${offer.id})"
                            class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-eye mr-1"></i>View
                    </button>

                    ${offer.est_actif !== 0 ? `
                        <button onclick="reserveOffer(${offer.id})"
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                            <i class="fas fa-calendar-check mr-1"></i>Reserve
                        </button>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    return card;
}

/**
 * Get image URL from image_path
 */
function getImageUrl(imagePath) {
    // image_path already includes "offers/" directory
    // Example: "offers/KiXWLHfMtJRMC1Qaj7D9861xBv41uLlzjGtmBYHp.jpg"
    // Return HTTP URL that Laravel can serve
    return `http://localhost:8000/storage/${imagePath}`;
}

/**
 * Handle search functionality
 */
function handleSearch() {
    const destination = destinationInput ? destinationInput.value.toLowerCase().trim() : '';
    const selectedDate = dateInput ? dateInput.value : '';
    const priceRange = priceRangeSelect ? priceRangeSelect.value : 'any';

    filteredOffers = allOffers.filter(offer => {
        // Destination filter
        if (destination && offer.destination && !offer.destination.toLowerCase().includes(destination)) {
            return false;
        }

        // Date filter
        if (selectedDate && offer.date_debut) {
            const offerStartDate = new Date(offer.date_debut);
            const searchDate = new Date(selectedDate);
            if (offerStartDate > searchDate) {
                return false;
            }
        }

        // Price filter
        if (priceRange !== 'any' && offer.prix) {
            const price = parseFloat(offer.prix);
            switch (priceRange) {
                case 'under-500':
                    if (price >= 500) return false;
                    break;
                case '500-1000':
                    if (price < 500 || price > 1000) return false;
                    break;
                case '1000-2000':
                    if (price < 1000 || price > 2000) return false;
                    break;
                case 'over-2000':
                    if (price <= 2000) return false;
                    break;
            }
        }

        return true;
    });

    currentPage = 1;
    displayOffers();
}

/**
 * Apply category filter
 */
function applyFilter(filterType) {
    if (filterType === 'all') {
        filteredOffers = [...allOffers];
    } else {
        // Filter by category (you can enhance this based on your offer structure)
        filteredOffers = allOffers.filter(offer => {
            const title = (offer.titre || '').toLowerCase();
            const description = (offer.description || '').toLowerCase();
            const destination = (offer.destination || '').toLowerCase();

            return title.includes(filterType) ||
                   description.includes(filterType) ||
                   destination.includes(filterType);
        });
    }

    currentPage = 1;
    displayOffers();
}

/**
 * Load more offers
 */
function loadMoreOffers() {
    currentPage++;
    displayOffers();
}

/**
 * Show loading indicator
 */
function showLoading() {
    if (loadingElement) {
        loadingElement.classList.remove('hidden');
    }
}

/**
 * Hide loading indicator
 */
function hideLoading() {
    if (loadingElement) {
        loadingElement.classList.add('hidden');
    }
}

/**
 * Show error message
 */
function showError(message) {
    if (errorElement) {
        const errorMessageElement = document.getElementById('error-message');
        if (errorMessageElement) {
            errorMessageElement.textContent = message;
        }
        errorElement.classList.remove('hidden');
    }
}

/**
 * Hide error message
 */
function hideError() {
    if (errorElement) {
        errorElement.classList.add('hidden');
    }
}

/**
 * Show load more button
 */
function showLoadMoreButton() {
    if (loadMoreBtn) {
        loadMoreBtn.classList.remove('hidden');
    }
}

/**
 * Hide load more button
 */
function hideLoadMoreButton() {
    if (loadMoreBtn) {
        loadMoreBtn.classList.add('hidden');
    }
}

// Global functions for offer interactions
window.viewOfferDetails = function(offerId) {
    console.log('Viewing offer details for ID:', offerId);
    // This function should be implemented to show offer details modal
    // The modal HTML is already present in the offers.html file
    showOfferDetailModal(offerId);
};

window.reserveOffer = function(offerId) {
    console.log('Reserving offer ID:', offerId);
    // This function should be implemented to handle reservations
    // The reservation modal HTML is already present in the offers.html file
    showReservationModal(offerId);
};

/**
 * Show offer detail modal
 */
async function showOfferDetailModal(offerId) {
    const modal = document.getElementById('offer-detail-modal');
    if (!modal) return;

    try {
        // Show modal
        modal.classList.remove('hidden');

        // Show loading state
        showDetailLoading();

        // Fetch offer details
        const offer = await offersAPI.getOfferById(offerId);

        // Populate modal with offer data
        populateOfferDetailModal(offer);

    } catch (error) {
        console.error('Error loading offer details:', error);
        // You could show an error message in the modal here
    }
}

/**
 * Show reservation modal
 */
function showReservationModal(offerId) {
    const modal = document.getElementById('reservation-modal');
    if (!modal) return;

    // Find the offer in our loaded offers
    const offer = allOffers.find(o => o.id === offerId);
    if (!offer) return;

    // Populate reservation modal
    populateReservationModal(offer);

    // Show modal
    modal.classList.remove('hidden');
}

/**
 * Show loading state in detail modal
 */
function showDetailLoading() {
    const titleElement = document.getElementById('detail-offer-title');
    const descriptionElement = document.getElementById('detail-description');
    const destinationElement = document.getElementById('detail-destination');
    const datesElement = document.getElementById('detail-dates');
    const priceElement = document.getElementById('detail-price');

    if (titleElement) titleElement.textContent = 'Loading...';
    if (descriptionElement) descriptionElement.textContent = 'Loading...';
    if (destinationElement) destinationElement.textContent = 'Loading...';
    if (datesElement) datesElement.textContent = 'Loading...';
    if (priceElement) priceElement.textContent = 'Loading...';
}

/**
 * Populate offer detail modal
 */
function populateOfferDetailModal(offer) {
    // Update title
    const titleElement = document.getElementById('detail-offer-title');
    if (titleElement) {
        titleElement.textContent = offer.titre || 'Untitled Offer';
    }

    // Update description
    const descriptionElement = document.getElementById('detail-description');
    if (descriptionElement) {
        descriptionElement.textContent = offer.description || 'No description available.';
    }

    // Update destination
    const destinationElement = document.getElementById('detail-destination');
    if (destinationElement) {
        destinationElement.textContent = offer.destination || 'Destination TBD';
    }

    // Update dates
    const datesElement = document.getElementById('detail-dates');
    if (datesElement) {
        const startDate = offer.date_debut ? new Date(offer.date_debut).toLocaleDateString() : '';
        const endDate = offer.date_fin ? new Date(offer.date_fin).toLocaleDateString() : '';
        const dateRange = startDate && endDate ? `${startDate} - ${endDate}` : 'Dates TBD';
        datesElement.textContent = dateRange;
    }

    // Update price
    const priceElement = document.getElementById('detail-price');
    if (priceElement) {
        const price = offer.prix ? `€${offer.prix}` : 'Price on request';
        priceElement.textContent = price;
    }

    // Update main image
    const mainImageElement = document.getElementById('detail-main-image');
    if (mainImageElement && offer.images && offer.images.length > 0) {
        const imageUrl = getImageUrl(offer.images[0].image_path);
        mainImageElement.src = imageUrl;
        mainImageElement.alt = offer.titre || 'Offer image';
    }

    // Setup reserve button
    const reserveButton = document.getElementById('reserve-from-detail');
    if (reserveButton) {
        reserveButton.onclick = () => {
            // Close detail modal
            document.getElementById('offer-detail-modal').classList.add('hidden');
            // Show reservation modal
            showReservationModal(offer.id);
        };
    }
}

/**
 * Populate reservation modal
 */
function populateReservationModal(offer) {
    // Update offer title
    const titleElement = document.getElementById('offer-title');
    if (titleElement) {
        titleElement.textContent = offer.titre || 'Untitled Offer';
    }

    // Update destination
    const destinationElement = document.getElementById('offer-destination');
    if (destinationElement) {
        destinationElement.textContent = offer.destination || 'Destination TBD';
    }

    // Update dates
    const datesElement = document.getElementById('offer-dates');
    if (datesElement) {
        const startDate = offer.date_debut ? new Date(offer.date_debut).toLocaleDateString() : '';
        const endDate = offer.date_fin ? new Date(offer.date_fin).toLocaleDateString() : '';
        const dateRange = startDate && endDate ? `${startDate} - ${endDate}` : 'Dates TBD';
        datesElement.textContent = dateRange;
    }

    // Check if visa warning should be shown
    const destination = (offer.destination || '').toLowerCase();
    const isAlgeria = destination.includes('algeria') ||
                     destination.includes('algérie') ||
                     destination.includes('alger') ||
                     destination.includes('algiers') ||
                     destination.includes('oran') ||
                     destination.includes('constantine') ||
                     destination.includes('annaba');

    // Show/hide visa warning
    const visaWarning = document.getElementById('visa-warning');
    if (visaWarning) {
        if (!isAlgeria && destination.trim() !== '') {
            visaWarning.classList.remove('hidden');
            visaWarning.innerHTML = `
                <div class="flex items-center p-3 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-lg">
                    <i class="fas fa-passport text-yellow-600 dark:text-yellow-400 mr-2"></i>
                    <div class="text-sm text-yellow-800 dark:text-yellow-200">
                        <strong>Visa Check Required:</strong> This destination is outside Algeria. You will need to verify your visa before completing the reservation.
                    </div>
                </div>
            `;
        } else {
            visaWarning.classList.add('hidden');
        }
    }

    // Setup confirm button
    const confirmButton = document.getElementById('confirm-reservation');
    if (confirmButton) {
        confirmButton.onclick = () => handleReservationConfirm(offer.id);

        // Update button text based on visa requirement
        if (!isAlgeria && destination.trim() !== '') {
            confirmButton.innerHTML = '<i class="fas fa-passport mr-2"></i>Proceed to Visa Check';
        } else {
            confirmButton.innerHTML = '<i class="fas fa-calendar-check mr-2"></i>Confirm Reservation';
        }
    }
}

/**
 * Handle reservation confirmation
 */
async function handleReservationConfirm(offerId) {
    const modal = document.getElementById('reservation-modal');
    const loadingElement = document.getElementById('modal-loading');
    const errorElement = document.getElementById('modal-error');
    const successElement = document.getElementById('modal-success');
    const detailsElement = document.getElementById('reservation-details');

    try {
        // Show loading
        if (loadingElement) loadingElement.classList.remove('hidden');
        if (errorElement) errorElement.classList.add('hidden');
        if (successElement) successElement.classList.add('hidden');
        if (detailsElement) detailsElement.classList.add('hidden');

        // Find the offer to check destination
        const offer = allOffers.find(o => o.id === offerId);
        if (!offer) {
            throw new Error('Offer not found');
        }

        // Check if destination is outside Algeria and requires visa check
        const destination = (offer.destination || '').toLowerCase();
        const isAlgeria = destination.includes('algeria') ||
                         destination.includes('algérie') ||
                         destination.includes('alger') ||
                         destination.includes('algiers') ||
                         destination.includes('oran') ||
                         destination.includes('constantine') ||
                         destination.includes('annaba');

        if (!isAlgeria && destination.trim() !== '') {
            // Store offer ID for visa check page
            localStorage.setItem('pending_reservation_offer_id', offerId);

            // Close modal
            if (modal) modal.classList.add('hidden');

            // Redirect to visa check page
            window.location.href = '/reservation/checkvisa.html';
            return;
        }

        // For Algeria destinations or empty destinations, proceed with direct reservation
        await offersAPI.reserveOffer(offerId);

        // Show success
        if (successElement) {
            successElement.classList.remove('hidden');
            const successMessage = document.getElementById('modal-success-message');
            if (successMessage) {
                successMessage.textContent = 'Your reservation has been confirmed!';
            }
        }

        // Auto-close modal after 3 seconds
        setTimeout(() => {
            if (modal) modal.classList.add('hidden');
        }, 3000);

    } catch (error) {
        console.error('Reservation error:', error);

        // Show error
        if (errorElement) {
            errorElement.classList.remove('hidden');
            const errorMessage = document.getElementById('modal-error-message');
            if (errorMessage) {
                errorMessage.textContent = error.message || 'Failed to make reservation. Please try again.';
            }
        }
    } finally {
        // Hide loading
        if (loadingElement) loadingElement.classList.add('hidden');
        if (detailsElement) detailsElement.classList.remove('hidden');
    }
}

// Setup modal close handlers
document.addEventListener('DOMContentLoaded', function() {
    // Close detail modal
    const closeDetailModal = document.getElementById('close-detail-modal');
    const closeDetailBtn = document.getElementById('close-detail-btn');
    if (closeDetailModal) {
        closeDetailModal.addEventListener('click', () => {
            document.getElementById('offer-detail-modal').classList.add('hidden');
        });
    }
    if (closeDetailBtn) {
        closeDetailBtn.addEventListener('click', () => {
            document.getElementById('offer-detail-modal').classList.add('hidden');
        });
    }

    // Close reservation modal
    const closeModal = document.getElementById('close-modal');
    const cancelReservation = document.getElementById('cancel-reservation');
    if (closeModal) {
        closeModal.addEventListener('click', () => {
            document.getElementById('reservation-modal').classList.add('hidden');
        });
    }
    if (cancelReservation) {
        cancelReservation.addEventListener('click', () => {
            document.getElementById('reservation-modal').classList.add('hidden');
        });
    }
});

/**
 * Check and display last reservation status
 */
function checkLastReservationStatus() {
    const lastReservationStatus = localStorage.getItem('last_reservation_status');
    const lastVisaCheck = localStorage.getItem('last_visa_check');
    const visaVerificationTime = localStorage.getItem('visa_verification_time');

    if (lastReservationStatus || lastVisaCheck) {
        showReservationStatusNotification(lastReservationStatus, lastVisaCheck, visaVerificationTime);

        // Clear status after showing (optional - remove if you want persistent status)
        setTimeout(() => {
            localStorage.removeItem('last_reservation_status');
            // Keep visa check status for longer
            // localStorage.removeItem('last_visa_check');
            // localStorage.removeItem('visa_verification_time');
        }, 10000); // Clear after 10 seconds
    }
}

/**
 * Show reservation status notification
 */
function showReservationStatusNotification(reservationStatus, visaStatus, verificationTime) {
    // Create notification container if it doesn't exist
    let notificationContainer = document.getElementById('status-notification');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'status-notification';
        notificationContainer.className = 'fixed top-20 right-4 z-50 max-w-md';
        document.body.appendChild(notificationContainer);
    }

    let notificationHTML = '';
    let notificationClass = '';

    // Determine notification content based on status
    if (reservationStatus === 'success') {
        notificationClass = 'bg-green-100 dark:bg-green-900/30 border-2 border-green-300 dark:border-green-700 text-green-800 dark:text-green-200';
        notificationHTML = `
            <div class="flex items-start p-4 rounded-lg shadow-lg">
                <i class="fas fa-check-circle text-2xl mr-3 mt-1 text-green-600"></i>
                <div class="flex-1">
                    <h4 class="font-bold text-lg mb-2">🎉 Réservation Confirmée!</h4>
                    <div class="space-y-1 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-passport text-green-600 mr-2"></i>
                            <span>Visa vérifié avec succès</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-calendar-check text-green-600 mr-2"></i>
                            <span>Réservation confirmée</span>
                        </div>
                    </div>
                </div>
                <button onclick="closeStatusNotification()" class="text-green-600 hover:text-green-800 ml-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    } else if (reservationStatus === 'failed') {
        notificationClass = 'bg-red-100 dark:bg-red-900/50 border-2 border-red-400 dark:border-red-600 text-red-800 dark:text-red-200';
        notificationHTML = `
            <div class="flex items-start p-4 rounded-lg shadow-lg">
                <i class="fas fa-times-circle text-2xl mr-3 mt-1 text-red-600"></i>
                <div class="flex-1">
                    <h4 class="font-bold text-lg mb-2">❌ Réservation Échouée</h4>
                    <div class="space-y-1 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-passport text-green-600 mr-2"></i>
                            <span>Visa vérifié avec succès</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-times text-red-600 mr-2"></i>
                            <span>Réservation échouée</span>
                        </div>
                    </div>
                    <p class="text-xs mt-2 text-red-600">Vous pouvez réessayer la réservation</p>
                </div>
                <button onclick="closeStatusNotification()" class="text-red-600 hover:text-red-800 ml-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    } else if (reservationStatus === 'network_error') {
        notificationClass = 'bg-orange-100 dark:bg-orange-900/30 border-2 border-orange-300 dark:border-orange-700 text-orange-800 dark:text-orange-200';
        notificationHTML = `
            <div class="flex items-start p-4 rounded-lg shadow-lg">
                <i class="fas fa-exclamation-circle text-2xl mr-3 mt-1 text-orange-600"></i>
                <div class="flex-1">
                    <h4 class="font-bold text-lg mb-2">🚫 Erreur de Connexion</h4>
                    <div class="space-y-1 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-passport text-green-600 mr-2"></i>
                            <span>Visa vérifié avec succès</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-wifi text-orange-600 mr-2"></i>
                            <span>Erreur de connexion</span>
                        </div>
                    </div>
                    <p class="text-xs mt-2 text-orange-600">Vérifiez votre connexion et réessayez</p>
                </div>
                <button onclick="closeStatusNotification()" class="text-orange-600 hover:text-orange-800 ml-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    } else if (visaStatus === 'not_visa') {
        notificationClass = 'bg-red-100 dark:bg-red-900/50 border-2 border-red-400 dark:border-red-600 text-red-800 dark:text-red-200';
        notificationHTML = `
            <div class="flex items-start p-4 rounded-lg shadow-lg">
                <i class="fas fa-times-circle text-2xl mr-3 mt-1 text-red-600"></i>
                <div class="flex-1">
                    <h4 class="font-bold text-lg mb-2" style="color: red;">❌ Not a Visa</h4>
                    <p class="text-sm">The uploaded document is not a valid visa.</p>
                    <p class="text-xs mt-2 text-red-600">Please upload a valid visa document to proceed with reservation</p>
                </div>
                <button onclick="closeStatusNotification()" class="text-red-600 hover:text-red-800 ml-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    } else if (visaStatus === 'wrong_destination') {
        notificationClass = 'bg-orange-100 dark:bg-orange-900/50 border-2 border-orange-400 dark:border-orange-600 text-orange-800 dark:text-orange-200';
        notificationHTML = `
            <div class="flex items-start p-4 rounded-lg shadow-lg">
                <i class="fas fa-exclamation-triangle text-2xl mr-3 mt-1 text-orange-600"></i>
                <div class="flex-1">
                    <h4 class="font-bold text-lg mb-2">⚠️ Wrong Destination</h4>
                    <p class="text-sm">The visa is not for the correct destination.</p>
                    <p class="text-xs mt-2 text-orange-600">Please upload a visa for the destination you're booking</p>
                </div>
                <button onclick="closeStatusNotification()" class="text-orange-600 hover:text-orange-800 ml-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    } else if (visaStatus === 'unclear') {
        notificationClass = 'bg-gray-100 dark:bg-gray-800 border-2 border-gray-400 dark:border-gray-600 text-gray-800 dark:text-gray-200';
        notificationHTML = `
            <div class="flex items-start p-4 rounded-lg shadow-lg">
                <i class="fas fa-question-circle text-2xl mr-3 mt-1 text-gray-600"></i>
                <div class="flex-1">
                    <h4 class="font-bold text-lg mb-2">❓ Unable to Verify</h4>
                    <p class="text-sm">Could not clearly identify the document.</p>
                    <p class="text-xs mt-2 text-gray-600">Please try uploading a clearer image</p>
                </div>
                <button onclick="closeStatusNotification()" class="text-gray-600 hover:text-gray-800 ml-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    }

    if (notificationHTML) {
        notificationContainer.className = `fixed top-20 right-4 z-50 max-w-md ${notificationClass} rounded-lg shadow-lg`;
        notificationContainer.innerHTML = notificationHTML;

        // Auto-hide after 8 seconds
        setTimeout(() => {
            closeStatusNotification();
        }, 8000);
    }
}

/**
 * Close status notification
 */
window.closeStatusNotification = function() {
    const notification = document.getElementById('status-notification');
    if (notification) {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }
};