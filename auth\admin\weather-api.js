// Weather API Integration for Admin Dashboard
const WEATHER_API_KEY = '5b2f5a6494b74aef940140514251505';
const WEATHER_API_BASE_URL = 'https://api.weatherapi.com/v1';

// DOM Elements
let weatherWidget;
let weatherLocation;
let weatherIcon;
let weatherTemp;
let weatherCondition;
let weatherDetails;
let weatherForecast;
let locationInput;
let searchWeatherBtn;

// Default location
let defaultLocation = 'Algiers';

// Initialize weather widget
document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    weatherWidget = document.getElementById('weather-widget');
    weatherLocation = document.getElementById('weather-location');
    weatherIcon = document.getElementById('weather-icon');
    weatherTemp = document.getElementById('weather-temp');
    weatherCondition = document.getElementById('weather-condition');
    weatherDetails = document.getElementById('weather-details');
    weatherForecast = document.getElementById('weather-forecast');
    locationInput = document.getElementById('weather-location-input');
    searchWeatherBtn = document.getElementById('search-weather-btn');

    // Add event listener to search button
    if (searchWeatherBtn) {
        searchWeatherBtn.addEventListener('click', function() {
            const location = locationInput.value.trim();
            if (location) {
                getWeatherData(location);
            }
        });
    }

    // Add event listener to location input for Enter key
    if (locationInput) {
        locationInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const location = locationInput.value.trim();
                if (location) {
                    getWeatherData(location);
                }
            }
        });
    }

    // Get weather for default location
    getWeatherData(defaultLocation);
    
    // Add event listener to offer select to update weather based on destination
    const offerSelect = document.getElementById('offer-id');
    if (offerSelect) {
        offerSelect.addEventListener('change', function() {
            const selectedOption = offerSelect.options[offerSelect.selectedIndex];
            const destination = selectedOption.getAttribute('data-destination');
            if (destination) {
                getWeatherData(destination);
                if (locationInput) {
                    locationInput.value = destination;
                }
            }
        });
    }
});

// Function to get weather data
async function getWeatherData(location) {
    try {
        showWeatherLoading();

        // Fetch current weather data
        const currentResponse = await fetch(`${WEATHER_API_BASE_URL}/current.json?key=${WEATHER_API_KEY}&q=${location}&aqi=no`);
        
        if (!currentResponse.ok) {
            throw new Error(`Weather API error: ${currentResponse.status}`);
        }
        
        const currentData = await currentResponse.json();
        
        // Fetch forecast data (3 days)
        const forecastResponse = await fetch(`${WEATHER_API_BASE_URL}/forecast.json?key=${WEATHER_API_KEY}&q=${location}&days=3&aqi=no&alerts=no`);
        
        if (!forecastResponse.ok) {
            throw new Error(`Weather API error: ${forecastResponse.status}`);
        }
        
        const forecastData = await forecastResponse.json();
        
        // Update the UI with weather data
        updateWeatherUI(currentData, forecastData);
        
    } catch (error) {
        console.error('Error fetching weather data:', error);
        showWeatherError(error.message);
    }
}

// Function to update weather UI
function updateWeatherUI(currentData, forecastData) {
    if (!weatherWidget) return;
    
    // Show weather widget
    weatherWidget.classList.remove('hidden');
    weatherWidget.classList.remove('animate-pulse');
    
    // Update current weather
    weatherLocation.textContent = `${currentData.location.name}, ${currentData.location.country}`;
    weatherIcon.src = `https:${currentData.current.condition.icon}`;
    weatherIcon.alt = currentData.current.condition.text;
    weatherTemp.textContent = `${currentData.current.temp_c}°C`;
    weatherCondition.textContent = currentData.current.condition.text;
    
    // Update weather details
    weatherDetails.innerHTML = `
        <div class="flex items-center justify-between">
            <div>
                <span class="text-gray-500 dark:text-gray-400">Humidity</span>
                <p class="font-medium">${currentData.current.humidity}%</p>
            </div>
            <div>
                <span class="text-gray-500 dark:text-gray-400">Wind</span>
                <p class="font-medium">${currentData.current.wind_kph} km/h</p>
            </div>
            <div>
                <span class="text-gray-500 dark:text-gray-400">Feels like</span>
                <p class="font-medium">${currentData.current.feelslike_c}°C</p>
            </div>
        </div>
    `;
    
    // Update forecast
    const forecastDays = forecastData.forecast.forecastday;
    let forecastHTML = '<div class="grid grid-cols-3 gap-2 mt-4">';
    
    forecastDays.forEach(day => {
        const date = new Date(day.date);
        const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
        
        forecastHTML += `
            <div class="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p class="text-sm font-medium">${dayName}</p>
                <img src="https:${day.day.condition.icon}" alt="${day.day.condition.text}" class="w-10 h-10 mx-auto my-1">
                <p class="text-sm">${Math.round(day.day.maxtemp_c)}° / ${Math.round(day.day.mintemp_c)}°</p>
            </div>
        `;
    });
    
    forecastHTML += '</div>';
    weatherForecast.innerHTML = forecastHTML;
}

// Function to show loading state
function showWeatherLoading() {
    if (!weatherWidget) return;
    
    weatherWidget.classList.remove('hidden');
    weatherWidget.classList.add('animate-pulse');
    
    weatherLocation.textContent = 'Loading...';
    weatherTemp.textContent = '--°C';
    weatherCondition.textContent = 'Loading weather data';
    weatherDetails.innerHTML = '';
    weatherForecast.innerHTML = '';
}

// Function to show error state
function showWeatherError(message) {
    if (!weatherWidget) return;
    
    weatherWidget.classList.remove('hidden');
    weatherWidget.classList.remove('animate-pulse');
    
    weatherLocation.textContent = 'Error';
    weatherTemp.textContent = '--°C';
    weatherCondition.textContent = message || 'Unable to load weather data';
    weatherDetails.innerHTML = '';
    weatherForecast.innerHTML = '';
}

// Function to get weather for a reservation destination
function getWeatherForDestination(destination) {
    if (destination) {
        getWeatherData(destination);
        if (locationInput) {
            locationInput.value = destination;
        }
    }
}

// Make the function available globally
window.getWeatherForDestination = getWeatherForDestination;
