document.addEventListener('DOMContentLoaded', () => {
    // Mobile menu toggle
    const hamburger = document.getElementById('hamburger');
    const mobileMenu = document.getElementById('mobile-menu');

    if (hamburger && mobileMenu) {
        hamburger.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // Tab switching functionality
    const hotelBtn = document.getElementById('hotel-btn');
    const voyageBtn = document.getElementById('voyage-btn');
    const hotelSection = document.getElementById('hotel-section');
    const voyageSection = document.getElementById('voyage-section');

    if (hotelBtn && voyageBtn && hotelSection && voyageSection) {
        hotelBtn.addEventListener('click', () => {
            hotelBtn.classList.add('active', 'text-indigo-600', 'border-indigo-600');
            hotelBtn.classList.remove('text-gray-600');
            voyageBtn.classList.remove('active', 'text-indigo-600', 'border-indigo-600');
            voyageBtn.classList.add('text-gray-600');
            hotelSection.classList.remove('hidden');
            voyageSection.classList.add('hidden');
            // Save active tab
            localStorage.setItem('activeTab', 'hotel');
        });

        voyageBtn.addEventListener('click', () => {
            voyageBtn.classList.add('active', 'text-indigo-600', 'border-indigo-600');
            voyageBtn.classList.remove('text-gray-600');
            hotelBtn.classList.remove('active', 'text-indigo-600', 'border-indigo-600');
            hotelBtn.classList.add('text-gray-600');
            voyageSection.classList.remove('hidden');
            hotelSection.classList.add('hidden');
            // Save active tab
            localStorage.setItem('activeTab', 'voyage');
        });

        // Restore active tab from localStorage
        const activeTab = localStorage.getItem('activeTab');
        if (activeTab === 'voyage') {
            voyageBtn.click();
        }
    }

    // Restore voyage filter if it exists
    const selectedDestination = localStorage.getItem('selectedVoyageDestination');
    if (selectedDestination !== null) {
        // Use setTimeout to ensure the DOM is fully loaded
        setTimeout(() => {
            filterVoyages(selectedDestination);
        }, 100);
    }
});

// Function to show voyage booking form
function showVoyageForm(packageId, packageName) {
    const voyageForm = document.getElementById('voyage-form');
    const packageIdInput = document.getElementById('package_id');
    const packageNameInput = document.getElementById('package_name');

    if (voyageForm && packageIdInput && packageNameInput) {
        packageIdInput.value = packageId;
        packageNameInput.value = packageName;
        voyageForm.classList.remove('hidden');
        voyageForm.scrollIntoView({ behavior: 'smooth' });
    }
}

// Function to filter voyage packages by destination
function filterVoyages(destination) {
    const voyageCards = document.querySelectorAll('.voyage-card');
    const allDestinationsBtn = document.querySelector('button[onclick="filterVoyages(\'\')"]');
    const destinationBtns = document.querySelectorAll('button[onclick^="filterVoyages(\'"]');

    // Save the selected destination to localStorage
    localStorage.setItem('selectedVoyageDestination', destination);

    // Reset all buttons
    destinationBtns.forEach(btn => {
        btn.classList.remove('bg-indigo-600', 'text-white');
        btn.classList.add('bg-gray-200', 'text-gray-800');
    });

    if (destination === '') {
        // All destinations selected
        allDestinationsBtn.classList.remove('bg-gray-200', 'text-gray-800');
        allDestinationsBtn.classList.add('bg-indigo-600', 'text-white');
        voyageCards.forEach(card => {
            card.style.display = 'block';
        });
    } else {
        // Specific destination selected
        allDestinationsBtn.classList.remove('bg-indigo-600', 'text-white');
        allDestinationsBtn.classList.add('bg-gray-200', 'text-gray-800');

        // Highlight the selected destination button
        const selectedBtn = document.querySelector(`button[onclick="filterVoyages('${destination}')"]`);
        if (selectedBtn) {
            selectedBtn.classList.remove('bg-gray-200', 'text-gray-800');
            selectedBtn.classList.add('bg-indigo-600', 'text-white');
        }

        voyageCards.forEach(card => {
            if (card.dataset.destination === destination) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
}

// Function to clear voyage filters
function clearVoyageFilters() {
    // Clear localStorage
    localStorage.removeItem('selectedVoyageDestination');

    // Reset to show all destinations
    filterVoyages('');

    // Hide voyage form if it's visible
    const voyageForm = document.getElementById('voyage-form');
    if (voyageForm) {
        voyageForm.classList.add('hidden');
    }
}
