// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Load the navbar
    loadNavbar();
    
    // Initialize navbar functionality after it's loaded
    function initNavbar() {
        // User menu toggle
        const userMenuButton = document.querySelector('.user-menu-button');
        const userMenuDropdown = document.querySelector('.user-menu-dropdown');
        
        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', function() {
                userMenuDropdown.classList.toggle('hidden');
            });
            
            // Close the dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userMenuButton.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                    userMenuDropdown.classList.add('hidden');
                }
            });
        }
        
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
                
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('animate__fadeIn');
                    mobileMenu.classList.remove('animate__fadeOut');
                } else {
                    mobileMenu.classList.add('animate__fadeOut');
                    mobileMenu.classList.remove('animate__fadeIn');
                }
            });
        }
        
        // Dark mode toggle functionality
        const themeToggleBtn = document.getElementById('theme-toggle');
        const themeToggleBtnMobile = document.getElementById('theme-toggle-mobile');
        const themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
        const themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');
        const themeToggleDarkIconMobile = document.getElementById('theme-toggle-dark-icon-mobile');
        const themeToggleLightIconMobile = document.getElementById('theme-toggle-light-icon-mobile');
        
        // Set initial theme based on localStorage or system preference
        if (localStorage.getItem('color-theme') === 'dark' ||
            (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
            themeToggleLightIcon.classList.add('hidden');
            themeToggleDarkIcon.classList.remove('hidden');
            themeToggleLightIconMobile.classList.add('hidden');
            themeToggleDarkIconMobile.classList.remove('hidden');
        } else {
            document.documentElement.classList.remove('dark');
            themeToggleDarkIcon.classList.add('hidden');
            themeToggleLightIcon.classList.remove('hidden');
            themeToggleDarkIconMobile.classList.add('hidden');
            themeToggleLightIconMobile.classList.remove('hidden');
        }
        
        // Toggle theme function
        function toggleTheme() {
            // Toggle icons
            themeToggleDarkIcon.classList.toggle('hidden');
            themeToggleLightIcon.classList.toggle('hidden');
            themeToggleDarkIconMobile.classList.toggle('hidden');
            themeToggleLightIconMobile.classList.toggle('hidden');
            
            // Toggle dark class on html element
            if (themeToggleDarkIcon.classList.contains('hidden')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('color-theme', 'light');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('color-theme', 'dark');
            }
        }
        
        // Add event listeners to toggle buttons
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', toggleTheme);
        }
        
        if (themeToggleBtnMobile) {
            themeToggleBtnMobile.addEventListener('click', toggleTheme);
        }
        
        // Check authentication status and update UI
        updateAuthUI();
        
        // Add logout functionality
        const logoutButton = document.getElementById('logout-button');
        const mobileLogoutButton = document.getElementById('mobile-logout-button');
        
        if (logoutButton) {
            logoutButton.addEventListener('click', handleLogout);
        }
        
        if (mobileLogoutButton) {
            mobileLogoutButton.addEventListener('click', handleLogout);
        }
    }
    
    // Function to load the navbar
    function loadNavbar() {
        const navbarContainer = document.getElementById('navbar-container');
        
        if (navbarContainer) {
            fetch('/components/navbar.html')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to load navbar');
                    }
                    return response.text();
                })
                .then(html => {
                    navbarContainer.innerHTML = html;
                    initNavbar();
                })
                .catch(error => {
                    console.error('Error loading navbar:', error);
                    navbarContainer.innerHTML = `
                        <nav class="bg-white dark:bg-gray-800 shadow-sm">
                            <div class="max-w-7xl mx-auto px-4 py-4">
                                <div class="flex justify-between">
                                    <a href="/index/index.html" class="flex items-center">
                                        <i class="fas fa-globe-americas text-blue-600 dark:text-blue-400 text-2xl mr-2"></i>
                                        <span class="font-bold text-xl text-gray-900 dark:text-white">VoyageGlobe</span>
                                    </a>
                                    <div>
                                        <a href="/auth/login.html" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium">
                                            <i class="fas fa-sign-in-alt mr-1"></i> Connexion
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </nav>
                    `;
                });
        }
    }
    
    // Function to update UI based on authentication status
    function updateAuthUI() {
        const token = localStorage.getItem('auth_token');
        const userData = localStorage.getItem('user_data');
        const userRole = localStorage.getItem('user_role');
        
        const loggedOutMenu = document.getElementById('logged-out-menu');
        const loggedInMenu = document.getElementById('logged-in-menu');
        const mobileLoggedOutMenu = document.getElementById('mobile-logged-out-menu');
        const mobileLoggedInMenu = document.getElementById('mobile-logged-in-menu');
        const adminDashboardLink = document.getElementById('admin-dashboard-link');
        const mobileAdminDashboardLink = document.getElementById('mobile-admin-dashboard-link');
        
        if (token && userData) {
            // User is logged in
            if (loggedOutMenu) loggedOutMenu.classList.add('hidden');
            if (loggedInMenu) loggedInMenu.classList.remove('hidden');
            if (mobileLoggedOutMenu) mobileLoggedOutMenu.classList.add('hidden');
            if (mobileLoggedInMenu) mobileLoggedInMenu.classList.remove('hidden');
            
            // Parse user data
            try {
                const user = JSON.parse(userData);
                const name = user.name || (user.data && user.data.name) || 'User';
                const email = user.email || (user.data && user.data.email) || '';
                
                // Set user initials
                const initials = name.split(' ')
                    .map(n => n[0])
                    .join('')
                    .toUpperCase()
                    .substring(0, 2);
                
                const userInitialsElements = document.querySelectorAll('#user-initials, #mobile-user-initials');
                userInitialsElements.forEach(el => {
                    if (el) el.textContent = initials;
                });
                
                // Set user name and email in mobile menu
                const userNameElement = document.getElementById('mobile-user-name');
                const userEmailElement = document.getElementById('mobile-user-email');
                
                if (userNameElement) userNameElement.textContent = name;
                if (userEmailElement) userEmailElement.textContent = email;
                
                // Show/hide admin dashboard link based on role
                if (userRole === 'admin') {
                    if (adminDashboardLink) adminDashboardLink.classList.remove('hidden');
                    if (mobileAdminDashboardLink) mobileAdminDashboardLink.classList.remove('hidden');
                } else {
                    if (adminDashboardLink) adminDashboardLink.classList.add('hidden');
                    if (mobileAdminDashboardLink) mobileAdminDashboardLink.classList.add('hidden');
                }
            } catch (error) {
                console.error('Error parsing user data:', error);
            }
        } else {
            // User is logged out
            if (loggedOutMenu) loggedOutMenu.classList.remove('hidden');
            if (loggedInMenu) loggedInMenu.classList.add('hidden');
            if (mobileLoggedOutMenu) mobileLoggedOutMenu.classList.remove('hidden');
            if (mobileLoggedInMenu) mobileLoggedInMenu.classList.add('hidden');
        }
    }
    
    // Function to handle logout
    function handleLogout(event) {
        event.preventDefault();
        
        const token = localStorage.getItem('auth_token');
        
        if (token) {
            // Call logout API
            fetch('http://localhost:8000/api/auth/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                // Clear local storage regardless of API response
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user_data');
                localStorage.removeItem('user_role');
                
                // Redirect to home page
                window.location.href = '/index/index.html';
            })
            .catch(error => {
                console.error('Logout error:', error);
                // Still clear local storage and redirect on error
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user_data');
                localStorage.removeItem('user_role');
                window.location.href = '/index/index.html';
            });
        } else {
            // If no token, just redirect
            window.location.href = '/index/index.html';
        }
    }
});
