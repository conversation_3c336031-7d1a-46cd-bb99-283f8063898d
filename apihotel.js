
document.getElementById('searchBtn').addEventListener('click', searchHotels);
document.getElementById('closeModalBtn').addEventListener('click', closeModal);

// Set default dates (today and tomorrow)
const today = new Date();
const tomorrow = new Date();
tomorrow.setDate(today.getDate() + 1);

// Format dates as YYYY-MM-DD
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

document.getElementById('checkin').value = formatDate(today);
document.getElementById('checkout').value = formatDate(tomorrow);

// API Key - vous devriez remplacer cette clé par une qui est valide
const API_KEY = '58afd0fe58mshcbffdffde83f664p1a6937jsnf96c23387534';

async function searchHotels() {
    const city = document.getElementById('city').value.trim();
    const checkin = document.getElementById('checkin').value;
    const checkout = document.getElementById('checkout').value;

    // Hide debug info
    document.getElementById('debug').classList.add('hidden');

    if (!city) {
        showError('Veuillez entrer une ville');
        return;
    }

    if (!checkin || !checkout) {
        showError('Veuillez sélectionner les dates d\'arrivée et de départ');
        return;
    }

    // Show loading indicator
    document.getElementById('loading').classList.remove('hidden');
    document.getElementById('results').innerHTML = '';
    hideError();

    try {
        // Utilisation de la locale en-gb pour une meilleure compatibilité
        const searchUrl = `https://booking-com.p.rapidapi.com/v1/hotels/locations?name=${encodeURIComponent(city)}&locale=en-gb`;

        const options = {
            method: 'GET',
            headers: {
                'x-rapidapi-key': API_KEY,
                'x-rapidapi-host': 'booking-com.p.rapidapi.com'
            }
        };

        // Log pour déboguer
        console.log("Calling API for locations:", searchUrl);
        showDebug(`Recherche de la destination: ${city}`);

        // Step 1: Get destination ID for the city
        const locationResponse = await fetch(searchUrl, options);

        if (!locationResponse.ok) {
            const errorText = await locationResponse.text();
            console.error("Location API error:", locationResponse.status, errorText);
            showDebug(`Erreur API: ${locationResponse.status} - ${errorText}`);
            throw new Error(`La recherche de destination a échoué avec le statut ${locationResponse.status}`);
        }

        const locationData = await locationResponse.json();
        console.log("Location data:", locationData);

        if (!locationData || locationData.length === 0) {
            throw new Error('Aucune destination trouvée pour cette ville');
        }

        // Get the first hotel destination (dest_type = city)
        const cityDestination = locationData.find(loc => loc.dest_type === 'city');

        // If no city found, try to use the first result regardless of type
        const destination = cityDestination || locationData[0];

        if (!destination) {
            throw new Error('Aucune destination trouvée');
        }

        showDebug(`Destination trouvée: ${destination.name} (ID: ${destination.dest_id})`);

        // Step 2: Search hotels using the dest_id
        const hotelsUrl = `https://booking-com.p.rapidapi.com/v1/hotels/search?checkin_date=${checkin}&checkout_date=${checkout}&dest_id=${destination.dest_id}&dest_type=${destination.dest_type}&units=metric&order_by=popularity&filter_by_currency=EUR&locale=en-gb&adults_number=2&room_number=1&include_adjacency=true`;

        console.log("Calling hotels API:", hotelsUrl);
        showDebug(`Recherche d'hôtels pour: ${destination.name}`);

        const hotelsResponse = await fetch(hotelsUrl, options);

        if (!hotelsResponse.ok) {
            const errorText = await hotelsResponse.text();
            console.error("Hotels API error:", hotelsResponse.status, errorText);
            showDebug(`Erreur API hôtels: ${hotelsResponse.status} - ${errorText}`);
            throw new Error(`La recherche d'hôtels a échoué avec le statut ${hotelsResponse.status}`);
        }

        const hotelsData = await hotelsResponse.json();
        console.log("Hotels data:", hotelsData);

        // Check if we have results property
        if (!hotelsData.result && hotelsData.results) {
            hotelsData.result = hotelsData.results; // Adapt to API changes
        }

        displayResults(hotelsData.result || [], destination.name);

        // Show how many hotels found
        if (hotelsData.result) {
            showDebug(`${hotelsData.result.length} hôtels trouvés à ${destination.name}`);
        }

    } catch (error) {
        console.error('Erreur:', error);
        showError(error.message);
    } finally {
        document.getElementById('loading').classList.add('hidden');
    }
}

function displayResults(hotels, cityName) {
    const resultsContainer = document.getElementById('results');

    if (!hotels || !hotels.length) {
        resultsContainer.innerHTML = `
            <div class="col-span-full text-center py-16">
                <div class="max-w-md mx-auto">
                    <div class="bg-gray-100 dark:bg-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-search text-3xl text-gray-400"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Aucun hôtel trouvé</h3>
                    <p class="text-gray-500 dark:text-gray-400">Aucun hôtel trouvé à ${cityName} pour les dates sélectionnées.</p>
                    <p class="text-sm text-gray-400 dark:text-gray-500 mt-2">Essayez de modifier vos dates ou votre destination.</p>
                </div>
            </div>`;
        return;
    }

    // Show modern header with results count
    resultsContainer.innerHTML = `
        <div class="col-span-full mb-8">
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-3xl font-bold mb-2">
                            <i class="fas fa-map-marker-alt mr-3"></i>Hôtels à ${cityName}
                        </h2>
                        <p class="text-blue-100">
                            <i class="fas fa-hotel mr-2"></i>${hotels.length} hôtel${hotels.length > 1 ? 's' : ''} trouvé${hotels.length > 1 ? 's' : ''}
                        </p>
                    </div>
                    <div class="hidden md:block">
                        <div class="bg-white/20 backdrop-blur-sm rounded-xl p-4">
                            <i class="fas fa-star text-yellow-300 text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Generate star rating function
    const generateStarRating = (rating) => {
        if (!rating || rating === 'Pas de note') return '<span class="text-gray-400 text-sm">Pas de note</span>';

        const numRating = parseFloat(rating);
        const fullStars = Math.floor(numRating / 2); // Convert 10-point to 5-point scale
        const hasHalfStar = (numRating / 2) % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHtml = '';

        // Full stars
        for (let i = 0; i < fullStars; i++) {
            starsHtml += '<i class="fas fa-star text-yellow-400"></i>';
        }

        // Half star
        if (hasHalfStar) {
            starsHtml += '<i class="fas fa-star-half-alt text-yellow-400"></i>';
        }

        // Empty stars
        for (let i = 0; i < emptyStars; i++) {
            starsHtml += '<i class="far fa-star text-gray-300"></i>';
        }

        return starsHtml;
    };

    // Add modern hotel cards
    hotels.forEach((hotel, index) => {
        const card = document.createElement('div');
        card.className = 'hotel-card group bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl overflow-hidden transition-all duration-300 hover:scale-105 border border-gray-100 dark:border-gray-700 cursor-pointer';
        card.dataset.hotelId = hotel.hotel_id;
        card.dataset.hotelUrl = hotel.url || '';
        card.addEventListener('click', () => showHotelDetails(hotel.hotel_id, hotel.hotel_name, hotel.url));

        // Format price
        const price = hotel.min_total_price || hotel.price_breakdown?.gross_price || 'Prix non disponible';
        const formattedPrice = typeof price === 'number' ? `${price} €` : price;

        // Format rating
        const rating = hotel.review_score || 'N/A';
        const ratingText = rating !== 'N/A' ? `${rating}/10` : 'Pas de note';

        card.innerHTML = `
            <!-- Image Section -->
            <div class="relative overflow-hidden">
                ${hotel.max_photo_url ? `
                    <img src="${hotel.max_photo_url}" alt="${hotel.hotel_name}"
                         class="w-full h-56 object-cover transition-transform duration-500 group-hover:scale-110">
                ` : `
                    <div class="w-full h-56 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-hotel text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">Image non disponible</p>
                        </div>
                    </div>
                `}

                <!-- Rating Badge -->
                <div class="absolute top-4 left-4 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm px-3 py-2 rounded-full shadow-lg">
                    <div class="flex items-center space-x-1">
                        ${generateStarRating(rating)}
                        <span class="text-sm font-semibold text-gray-800 dark:text-white ml-2">${ratingText}</span>
                    </div>
                </div>

                <!-- Price Badge -->
                <div class="absolute top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-full shadow-lg">
                    <div class="text-center">
                        <div class="text-lg font-bold">${formattedPrice}</div>
                        <div class="text-xs opacity-90">par nuit</div>
                    </div>
                </div>

                <!-- Gradient Overlay -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            <!-- Content Section -->
            <div class="p-6">
                <!-- Hotel Info -->
                <div class="mb-4">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 line-clamp-2">${hotel.hotel_name}</h3>
                    <div class="flex items-center text-gray-600 dark:text-gray-400 mb-3">
                        <i class="fas fa-map-marker-alt text-blue-600 dark:text-blue-400 mr-2"></i>
                        <span class="text-sm line-clamp-1">${hotel.address || hotel.district || cityName}</span>
                    </div>

                    ${hotel.distance_to_cc ? `
                        <div class="flex items-center text-gray-500 dark:text-gray-400 text-sm">
                            <i class="fas fa-route text-blue-600 dark:text-blue-400 mr-2"></i>
                            <span>${hotel.distance_to_cc} km du centre-ville</span>
                        </div>
                    ` : ''}
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-2">
                    <button onclick="showHotelDetails('${hotel.hotel_id}', '${hotel.hotel_name.replace(/'/g, "\\'")}', '${hotel.url || ''}')"
                            class="flex-1 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800
                                   text-white py-3 px-4 rounded-xl font-semibold text-sm
                                   transition-all duration-300 transform hover:scale-105 hover:shadow-lg
                                   flex items-center justify-center space-x-2">
                        <i class="fas fa-eye"></i>
                        <span>Détails</span>
                    </button>
                    <button onclick="openHotelBookingUrl('${hotel.url || ''}', '${hotel.hotel_name.replace(/'/g, "\\'")}')"
                            class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800
                                   text-white py-3 px-4 rounded-xl font-semibold text-sm
                                   transition-all duration-300 transform hover:scale-105 hover:shadow-lg
                                   flex items-center justify-center space-x-2">
                        <i class="fas fa-external-link-alt"></i>
                        <span>Réserver</span>
                    </button>
                </div>
            </div>
        `;
        resultsContainer.appendChild(card);
    });
}

async function showHotelDetails(hotelId, hotelName, hotelUrl) {
    // Show modal
    const modal = document.getElementById('hotelDetailModal');
    modal.classList.remove('hidden');

    // Set hotel name
    document.getElementById('modalHotelName').textContent = hotelName;

    // Show loading state
    document.getElementById('modalLoading').classList.remove('hidden');
    document.getElementById('modalContent').classList.add('hidden');
    document.getElementById('modalError').classList.add('hidden');

    try {
        // Fetch hotel details with English locale for better compatibility
        const url = `https://booking-com.p.rapidapi.com/v1/hotels/data?hotel_id=${hotelId}&locale=en-gb`;
        const options = {
            method: 'GET',
            headers: {
                'x-rapidapi-key': API_KEY,
                'x-rapidapi-host': 'booking-com.p.rapidapi.com'
            }
        };

        console.log("Getting hotel details:", url);

        const response = await fetch(url, options);
        if (!response.ok) {
            const errorText = await response.text();
            console.error("Hotel details API error:", response.status, errorText);
            throw new Error(`Échec de la récupération des détails de l'hôtel (statut ${response.status})`);
        }

        const result = await response.json();
        console.log("Données de l'hôtel:", result);
        displayHotelDetails(result, hotelUrl);
    } catch (error) {
        console.error('Erreur lors de la récupération des détails de l\'hôtel:', error);
        showModalError(error.message);
    } finally {
        document.getElementById('modalLoading').classList.add('hidden');
    }
}

function displayHotelDetails(hotelData, hotelUrl) {
    const contentDiv = document.getElementById('modalContent');

    // Format hotel description - removing HTML tags if present and limiting length
    let description = hotelData.description || 'Aucune description disponible';
    if (description.includes('<')) {
        // Simple HTML tag removal - for complex HTML you might want to use a proper parser
        description = description.replace(/<[^>]*>/g, '');
    }

    // Limit description length if it's very long
    if (description.length > 500) {
        description = description.substring(0, 500) + '...';
    }

    // Handle facilities - safely process the data
    let facilitiesHtml = '<p class="text-gray-500">Aucune information disponible</p>';

    if (hotelData.hotel_facilities) {
        let facilitiesList = [];

        // Check if hotel_facilities is an array
        if (Array.isArray(hotelData.hotel_facilities)) {
            facilitiesList = hotelData.hotel_facilities;
        }
        // Check if it's an object with values
        else if (typeof hotelData.hotel_facilities === 'object') {
            facilitiesList = Object.values(hotelData.hotel_facilities);
            // Flatten if any values are arrays
            facilitiesList = facilitiesList.flat();
        }

        if (facilitiesList.length > 0) {
            const displayFacilities = facilitiesList.slice(0, 10);

            facilitiesHtml = `<ul class="grid grid-cols-2 gap-2 mt-2">
                ${displayFacilities.map(facility =>
                    `<li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        ${facility}
                    </li>`
                ).join('')}
            </ul>`;

            if (facilitiesList.length > 10) {
                facilitiesHtml += `<p class="text-sm text-blue-600 mt-2">+ ${facilitiesList.length - 10} autres équipements</p>`;
            }
        }
    }

    // Get the direct booking URL from either source
    const bookingUrl = hotelData.url || hotelUrl || null;

    // Create photo gallery section if we have multiple photos
    let photoGalleryHtml = '';
    const photos = [];

    if (hotelData.main_photo_url) photos.push(hotelData.main_photo_url);
    if (hotelData.entrance_photo_url) photos.push(hotelData.entrance_photo_url);

    if (photos.length > 0) {
        photoGalleryHtml = `
            <div class="mt-4">
                <h3 class="text-lg font-semibold mb-2">Galerie photos</h3>
                <div class="grid grid-cols-2 gap-2">
                    ${photos.map(photo => `
                        <img src="${photo}" alt="Photo de l'hôtel" class="w-full h-32 object-cover rounded-lg cursor-pointer hover:opacity-90 transition"
                             onclick="window.open('${photo}', '_blank')">
                    `).join('')}
                </div>
            </div>`;
    }

    contentDiv.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                ${hotelData.main_photo_url ?
                    `<img src="${hotelData.main_photo_url}" alt="${hotelData.name}"
                          class="w-full h-64 object-cover rounded-lg cursor-pointer hover:opacity-90 transition"
                          onclick="window.open('${hotelData.main_photo_url}', '_blank')">` :
                    `<div class="w-full h-64 bg-gray-200 flex items-center justify-center rounded-lg">
                        <span class="text-gray-500">Pas d'image disponible</span>
                    </div>`
                }

                ${photoGalleryHtml}

                <div class="mt-4">
                    <h3 class="text-lg font-semibold mb-2">Emplacement</h3>
                    <p class="text-gray-700">${hotelData.address || 'Adresse non disponible'}</p>
                    <p class="text-gray-700 mt-1">${hotelData.city || ''}, ${hotelData.country || ''}</p>
                </div>
            </div>

            <div>
                <div class="mb-4">
                    <h3 class="text-lg font-semibold mb-2">À propos</h3>
                    <p class="text-gray-700 whitespace-pre-line">${description}</p>
                </div>

                <div class="mb-4">
                    <h3 class="text-lg font-semibold mb-2">Caractéristiques principales</h3>
                    ${facilitiesHtml}
                </div>

                <div class="flex items-center space-x-4 mt-4">
                    <div class="bg-blue-100 text-blue-800 py-1 px-3 rounded-full text-sm font-medium">
                        ${hotelData.class ? `Hôtel ${hotelData.class} étoiles` : 'Étoiles non disponibles'}
                    </div>
                    <div class="bg-blue-100 text-blue-800 py-1 px-3 rounded-full text-sm font-medium">
                        ${hotelData.review_score ? `Note: ${hotelData.review_score}/10` : 'Pas encore d\'avis'}
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6 pt-6 border-t border-gray-200">
            <h3 class="text-lg font-semibold mb-2">Informations de réservation</h3>
            ${bookingUrl ?
                `<a href="${bookingUrl}" target="_blank" class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md transition mt-2">
                    Voir cet hôtel sur Booking.com
                </a>` :
                `<p class="text-gray-700">
                    Pour réserver cet hôtel, veuillez visiter Booking.com ou contacter l'hôtel directement.
                </p>`
            }
        </div>
    `;

    contentDiv.classList.remove('hidden');
}

function closeModal() {
    document.getElementById('hotelDetailModal').classList.add('hidden');
}

// Function to open hotel booking URL
function openHotelBookingUrl(hotelUrl, hotelName) {
    if (hotelUrl && hotelUrl.trim() !== '') {
        // Open the hotel's booking URL from the API
        window.open(hotelUrl, '_blank');
    } else {
        // Fallback: Search for the hotel on Booking.com
        const searchQuery = encodeURIComponent(hotelName);
        window.open(`https://www.booking.com/searchresults.html?ss=${searchQuery}`, '_blank');
    }
}

function showModalError(message) {
    const errorElement = document.getElementById('modalError');
    document.getElementById('modalErrorMessage').textContent = message;
    errorElement.classList.remove('hidden');
}

function showError(message) {
    const errorElement = document.getElementById('error');
    document.getElementById('errorMessage').textContent = message;
    errorElement.classList.remove('hidden');
}

function hideError() {
    document.getElementById('error').classList.add('hidden');
}

function showDebug(message) {
    const debugElement = document.getElementById('debug');
    document.getElementById('debugInfo').textContent = message;
    debugElement.classList.remove('hidden');
}

// Close modal when clicking outside of it
window.addEventListener('click', (event) => {
    const modal = document.getElementById('hotelDetailModal');
    if (event.target === modal) {
        closeModal();
    }
});
